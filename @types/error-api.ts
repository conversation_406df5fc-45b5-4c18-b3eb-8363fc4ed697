import { NextResponse } from "next/server";
import { <PERSON>th<PERSON><PERSON><PERSON>, ParamsError, Credits402Error } from "@/@types/error";
import { notifyDevEvent } from "@/server/dev-notify.server";

export function handleApiError(error: any, eventName: string, options?: string) {
	console.error("API error:", error);

	const errorClasses = [AuthError, ParamsError, Credits402Error];
	const knownError = errorClasses.find((errorClass) => error instanceof errorClass);

	if (knownError) {
		return NextResponse.json({
			status: error.statusCode,
			message: error.message,
		});
	}

	notifyDevEvent(eventName, "Error", error.message, options);
	return NextResponse.json({ status: 500, error: error.message });
}
