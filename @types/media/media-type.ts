export enum MediaHeadType {
	Image = "image",
	Video = "video",
}

export enum MediaResultStatus {
	None = 0,
	PreparingFile = 1,
	UploadingFile = 11,
	Queued = 21,
	InProgress = 22,
	Completed = 31,
	Failed = 32,
	Expire = 400,
}
const mediaResultStatusInfos = [
	{
		status: MediaResultStatus.PreparingFile,
		statusText: "Preparing...",
		formatted: "Preparing",
	},
	{
		status: MediaResultStatus.UploadingFile,
		statusText: "Uploading...",
		formatted: "Uploading",
	},
	{
		status: MediaResultStatus.Queued,
		statusText: "Creating task...",
		formatted: "Queuing",
	},
	{
		status: MediaResultStatus.InProgress,
		statusText: "Generating...",
		formatted: "InProgress",
	},
	{
		status: MediaResultStatus.Completed,
		statusText: "Completed 🎉",
		formatted: "Completed",
	},
	{
		status: MediaResultStatus.Failed,
		statusText: "Generation failed 😢, please try again or contact support",
		formatted: "Failed",
	},
];
export const getMediaResultStatusText = (status: number) => {
	const statusInfo = mediaResultStatusInfos.find((statusInfo) => statusInfo.status === status);
	return statusInfo?.statusText ?? "Unknown";
};
export const getMediaResultStatusFormatted = (status: number) => {
	const statusInfo = mediaResultStatusInfos.find((statusInfo) => statusInfo.status === status);
	return statusInfo?.formatted ?? "Unknown";
};

// for admin
export type ImageResultStatusAdminType = {
	label: number;
	formatted: string;
};
export enum ImageResultStatusAdminTypeID {
	Pending = 0,
	Reject = 1,
	Irregular = 2,
	Accept = 10,
}
export const getImageResultStatusAdminTypes: ImageResultStatusAdminType[] = [
	{
		label: ImageResultStatusAdminTypeID.Pending,
		formatted: "Pending",
	},
	{
		label: ImageResultStatusAdminTypeID.Reject,
		formatted: "Reject",
	},
	{
		label: ImageResultStatusAdminTypeID.Irregular,
		formatted: "Irregular",
	},
	{
		label: ImageResultStatusAdminTypeID.Accept,
		formatted: "Accept",
	},
];
export const getImageresultStatusAdminTypeFormatted = (status: number): string => {
	const statusType = getImageResultStatusAdminTypes.find((statusType) => statusType.label === status);
	return statusType?.formatted ?? "Unknown";
};
