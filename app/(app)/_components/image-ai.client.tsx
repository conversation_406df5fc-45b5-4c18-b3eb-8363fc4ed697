"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { RectangleVertical, Square, RectangleHorizontal, Download, Icon, LoaderCircle, Loader2, ImagePlus, Trash } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getInitialImageModel, ImageModel, imageToImageModels, RECRAFT_3, textToImageModels } from "@/lib/utils-image-model";
import { coinsStack } from "@lucide/lab";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, fileToBase64, imageUrlToBase64 } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { Hint } from "@/components/ui/custom/hint";
import Link from "next/link";
import { Dropzone, DropzoneEmptyState } from "@/components/ui/kibo-ui/dropzone/index";
import { uploadFile } from "@/lib/file/upload-file";
import { CLICK_GEN_IMAGE_TO_IMAGE, CLICK_GEN_TEXT_TO_IMAGE } from "@/lib/umami-event-name";

type ImageSizeType = {
	ratio: string;
	width: number;
	height: number;
	label: string;
	icon: any;
};
const imageSizes: ImageSizeType[] = [
	{ ratio: "1:1", width: 1024, height: 1024, label: "Square", icon: Square },
	{ ratio: "2:3", width: 683, height: 1024, label: "Portrait", icon: RectangleVertical },
	{ ratio: "3:4", width: 768, height: 1024, label: "Traditional", icon: RectangleVertical },
	// { ratio: "4:5", width: 819, height: 1024, label: "Social Post", icon: RectangleVertical },
	{ ratio: "9:16", width: 576, height: 1024, label: "Social Story", icon: RectangleVertical },
	{ ratio: "3:2", width: 1024, height: 683, label: "Standard", icon: RectangleHorizontal },
	{ ratio: "4:3", width: 1024, height: 768, label: "Classic", icon: RectangleHorizontal },
	{ ratio: "16:9", width: 1024, height: 576, label: "Widescreen", icon: RectangleHorizontal },
];
const isImageModelAspectRatioExist = (model: ImageModel, imageSize: ImageSizeType) => {
	if (model.id.startsWith("imagen")) return ["1:1", "3:4", "4:3", "9:16", "16:9"].includes(imageSize.ratio);
	return true;
};
const isImageModelNumImagesExist = (model: ImageModel, numImages: number) => {
	if (model.id.startsWith("recraft")) return numImages === 1;
	return true;
};

export default function ImageGenerator({ imageToImage }: { imageToImage?: boolean }) {
	const searchParams = useSearchParams();
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	useEffect(() => {
		if (user && !userHasPaid) {
			setPlanBoxOpen(true);
		}
	}, []);

	const imageModels: ImageModel[] = imageToImage ? imageToImageModels : textToImageModels;

	const modelParam = searchParams.get("model");
	const initialModel = getInitialImageModel(modelParam, imageToImage);
	const [model, setModel] = useState<ImageModel>(initialModel);
	const [modelStyle, setModelStyle] = useState<string | null>(initialModel.modelStyle ? initialModel.modelStyle[0].id : null);
	const [prompt, setPrompt] = useState<string>("");
	const [imageSize, setImageSize] = useState<ImageSizeType>(imageSizes[0]);
	const [numImages, setNumImages] = useState<number>(1);
	const [image, setImage] = useState<{
		base64: string;
		url: string;
	} | null>(null);
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const handleLocalFileDrop = async (files: File[]) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		if (!files || files.length === 0) return;

		try {
			setUploadingImage(true);
			const { file_url } = await uploadFile(files[0]);
			// const file_url = "";
			const base64 = await fileToBase64(files[0]);
			setImage({
				url: file_url,
				base64,
			});
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [previewImageBase64s, setPreviewImageBase64s] = useState<string[] | null>(null);
	// const [previewImageBase64s, setPreviewImageBase64s] = useState<string[] | null>([
	// 	"https://static.youstylize.com/dev/image_result/202504/2025040701960f88c96272eb9f49f2e5e5ea7c0f.png",
	// ]);

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		try {
			setPreviewImageBase64s(Array(numImages).fill(null));
			setSubmitting(true);
			const { status, message, resultUrls } = await ofetch("/api/v1/image/generate-image", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					model: model.id,
					prompt: promtpTrim,
					size: {
						ratio: imageSize.ratio,
						width: imageSize.width,
						height: imageSize.height,
					},
					numImages: numImages,
					image: image?.url,
					modelStyle: modelStyle,
				},
			});
			handleError(status, message);
			refreshUser();

			// const resultUrls = [
			// 	"/static/images/demo.jpg",
			// 	"/static/images/demo.jpg",
			// 	// "/static/images/demo.jpg",
			// 	//  "/static/images/demo.jpg"
			// ];

			if (resultUrls && resultUrls.length > 0) {
				// Create a copy of the current array to modify
				const tempBase64s = Array(resultUrls.length).fill(null);
				// Process each URL and update the state as each completes
				for (let i = 0; i < resultUrls.length; i++) {
					const base64 = await imageUrlToBase64(resultUrls[i] as string);
					tempBase64s[i] = base64;
					setPreviewImageBase64s([...tempBase64s]);
				}
			}

			toast.success("Generate success.");
		} catch (error: any) {
			setPreviewImageBase64s(null);
			console.error("Failed to generate image:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [isImageDialogOpen, setIsImageDialogOpen] = useState<boolean>(false);
	const [dialogUrl, setDialogUrl] = useState<string>("");

	const [downloadingImageIndex, setDownloadingImageIndex] = useState<number | null>(null);

	return (
		<div className="flex min-h-svh w-full flex-col gap-4 overflow-hidden bg-zinc-900 px-4 py-4 md:flex-row">
			<div className="flex w-full shrink-0 flex-col rounded border border-zinc-700 bg-zinc-800 md:h-[90dvh] md:max-w-80 md:min-w-80 xl:max-w-[360px] xl:min-w-[360px]">
				<div className="flex flex-row items-center gap-3 border-b border-zinc-700 px-3 pt-2 pb-2 text-sm text-zinc-100 lg:px-4 lg:pt-3">
					<Link
						href="/ai-image-generator"
						className={cn(
							"font-[350]",
							imageToImage ? "text-zinc-400 hover:text-zinc-300" : "pointer-events-none underline decoration-2 underline-offset-12",
						)}
					>
						Text to Image
					</Link>
					<Link
						href="/image-to-image"
						className={cn(
							"font-[350]",
							imageToImage ? "pointer-events-none underline decoration-2 underline-offset-12" : "text-zinc-400 hover:text-zinc-300",
						)}
					>
						Image to Image
					</Link>
				</div>
				<div
					className={cn(
						"grow space-y-4 overflow-y-auto p-3 lg:p-4",
						"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
					)}
				>
					<div className="space-y-[6px]">
						<p className="text-xs text-zinc-300">Model</p>
						<Select
							value={model.id}
							onValueChange={(value) => {
								const newModel = imageModels.find((model) => model.id === value) ?? imageModels[0];
								if (!isImageModelAspectRatioExist(newModel, imageSize)) {
									setImageSize(imageSizes[0]);
								}
								if (!isImageModelNumImagesExist(newModel, numImages)) {
									setNumImages(1);
								}
								setModel(newModel);
								if (newModel.modelStyle) {
									setModelStyle(newModel.modelStyle[0].id);
								}
							}}
						>
							<SelectTrigger className="w-full cursor-pointer text-zinc-200 shadow-none ring-offset-0 focus:ring-0 dark:bg-zinc-900/50 dark:hover:bg-zinc-800">
								<SelectValue>
									<div className="flex flex-row items-center gap-2">
										{/* <Aperture className="h-4 w-4" /> */}
										<img className="size-[14px]" src={model.logo} alt="" />
										<span className="text-[13px] font-[350]">{model.name}</span>
									</div>
								</SelectValue>
							</SelectTrigger>
							<SelectContent className="bg-zinc-900">
								{imageModels.map((model, index) => {
									if (imageToImage && !model.imageToImage) return null;
									return (
										<SelectItem
											key={index}
											value={model.id}
											className="cursor-pointer text-[13px] font-[350] text-zinc-300 focus:bg-indigo-500 focus:text-zinc-200"
										>
											<p className="flex flex-row items-center gap-2">
												<img className="size-[14px] text-white" src={model.logo} alt="" />
												<span className="">{model.name}</span>
												{/* <span className="text-xs">{model.description}</span> */}
											</p>
										</SelectItem>
									);
								})}
							</SelectContent>
						</Select>
					</div>
					{imageToImage && (
						<div className="space-y-[6px]">
							<p className="text-xs text-zinc-300">Image</p>

							{image ? (
								<div className="group relative h-[144px] w-full rounded border border-zinc-700 bg-zinc-900/50 p-0 hover:bg-zinc-900/50">
									<img
										src={image.base64}
										alt="Model"
										className="h-[142px] w-full object-contain"
										onContextMenu={(e) => e.preventDefault()}
										onDragStart={(e) => e.preventDefault()}
									/>
									<div className="absolute inset-0 hidden items-center justify-center bg-black/50 group-hover:flex">
										<button
											onClick={() => setImage(null)}
											className="cursor-pointer rounded-full p-2 text-zinc-300/80 transition-colors hover:text-white"
										>
											<Trash className="size-4" />
										</button>
									</div>
								</div>
							) : (
								<div
									onClick={() => {
										if (!session) {
											setSignInBoxOpen(true);
										}
									}}
								>
									<Dropzone
										multiple={false}
										maxFiles={1}
										noClick={!session}
										onDragEnter={() => {
											if (!session) {
												setSignInBoxOpen(true);
												return;
											}
										}}
										onDrop={(files) => handleLocalFileDrop(files)}
										accept={{
											"image/jpeg": [".jpg", ".jpeg", ".png", ".webp"],
										}}
										onError={console.error}
										className="h-[144px] w-full cursor-pointer border border-zinc-700 p-0 dark:bg-zinc-900/50 dark:hover:bg-zinc-800"
									>
										<DropzoneEmptyState>
											<>
												{uploadingImage ? (
													<Loader2 className="animate-spin text-zinc-400" />
												) : (
													<div className="text-muted-foreground flex h-10 flex-col items-center gap-2 font-normal hover:text-zinc-400">
														<ImagePlus />
														<p>Click to upload an image</p>
													</div>
												)}
											</>
										</DropzoneEmptyState>
									</Dropzone>
								</div>
							)}
						</div>
					)}

					<div className="space-y-[6px]">
						<p className="text-xs text-zinc-300">Prompt</p>
						<Textarea
							placeholder="Describe your image"
							rows={5}
							maxLength={2000}
							className={cn(
								"max-h-36 min-h-24",
								"resize-none border-zinc-700 shadow-none focus-visible:shadow-sm focus-visible:ring-0 dark:bg-zinc-900/50",
								"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
							)}
							value={prompt}
							onChange={(e) => setPrompt(e.target.value)}
						/>
						{/* <Button variant="ghost" size="icon" className="absolute right-2 top-2">
											<Image className="h-4 w-4" />
										</Button> */}
					</div>

					{model.modelStyle && (
						<div className="space-y-[6px]">
							<p className="text-xs text-zinc-300">Style</p>
							<div
								className={cn(
									"grid gap-1 text-sm font-[350] text-zinc-200",
									model.modelStyle.length > 2 ? `grid-cols-${model.modelStyle.length}` : "grid-cols-2",
								)}
							>
								{model.modelStyle.map((modelStyleOption, index) => (
									<button
										key={index}
										onClick={() => setModelStyle(modelStyleOption.id)}
										className={cn(
											"flex h-8 w-full cursor-pointer flex-row items-center gap-2 rounded border border-zinc-700 px-3",
											modelStyle === modelStyleOption.id && "border-indigo-500",
										)}
									>
										<span className="text-[13px]">{modelStyleOption.name}</span>
									</button>
								))}
							</div>
						</div>
					)}

					{!(model.noAspectRatio === "all" || (model.noAspectRatio === "image-to-image" && imageToImage)) && (
						<div className="space-y-[6px]">
							<p className="text-xs text-zinc-300">Aspect ratio</p>
							<Select
								value={imageSize.ratio}
								onValueChange={(value) => setImageSize(imageSizes.find((size) => size.ratio === value) ?? imageSizes[0])}
							>
								<SelectTrigger className="w-full cursor-pointer border-zinc-700 text-zinc-200 shadow-none ring-offset-0 focus:ring-0 dark:bg-zinc-900/50 dark:hover:bg-zinc-800">
									<SelectValue>
										<div className="flex flex-row items-center gap-2">
											<imageSize.icon className="size-5 fill-current text-zinc-600" />
											<span className="text-[13px] font-[350]">{imageSize.ratio}</span>
										</div>
									</SelectValue>
								</SelectTrigger>
								<SelectContent className="bg-zinc-900">
									{imageSizes.map((size, index) => {
										if (!isImageModelAspectRatioExist(model, size)) return null;
										return (
											<SelectItem
												key={index}
												value={size.ratio}
												className="group cursor-pointer bg-zinc-900/50 text-[13px] font-[350] text-zinc-300 focus:bg-indigo-500 focus:text-zinc-200"
											>
												<p className="flex flex-row items-center gap-2">
													<size.icon className="size-5 fill-current text-zinc-600" />
													<span className="">{size.ratio}</span>
													<span className="text-[10px] text-zinc-400 group-hover:text-zinc-300">{size.label}</span>
												</p>
											</SelectItem>
										);
									})}
								</SelectContent>
							</Select>
						</div>
					)}
					<div className="space-y-[6px]">
						<p className="text-xs text-zinc-300">Number of images</p>
						<div className="flex flex-row gap-1 text-sm font-[350] text-zinc-200">
							<button
								onClick={() => setNumImages(1)}
								className={cn("aspect-square w-8 cursor-pointer rounded border border-zinc-700", numImages === 1 && "border-indigo-400")}
							>
								1
							</button>
							<button
								onClick={() => {
									setNumImages(2);
								}}
								disabled={model.id === RECRAFT_3.id}
								className={cn(
									"aspect-square w-8 cursor-pointer rounded border border-zinc-700 disabled:text-zinc-500",
									numImages === 2 && "border-indigo-400",
									model.id === RECRAFT_3.id && "cursor-not-allowed",
								)}
							>
								2
							</button>
							<button
								onClick={() => {
									if (!userHasPaid) {
										setPlanBoxOpen(true);
										return;
									}
									setNumImages(3);
								}}
								disabled={model.id === RECRAFT_3.id}
								className={cn(
									"aspect-square w-8 cursor-pointer rounded border border-zinc-700 disabled:text-zinc-500",
									numImages === 3 && "border-indigo-400",
									model.id === RECRAFT_3.id && "cursor-not-allowed",
								)}
							>
								3
							</button>
							<button
								onClick={() => {
									if (!userHasPaid) {
										setPlanBoxOpen(true);
										return;
									}
									setNumImages(4);
								}}
								disabled={model.id === RECRAFT_3.id}
								className={cn(
									"aspect-square w-8 cursor-pointer rounded border border-zinc-700 disabled:text-zinc-500",
									numImages === 4 && "border-indigo-400",
									model.id === RECRAFT_3.id && "cursor-not-allowed",
								)}
							>
								4
							</button>
						</div>
					</div>
				</div>

				<div className="space-y-0.5 border-t border-zinc-700 p-3 lg:p-4">
					<p className="flex flex-row items-center gap-1 text-[11px] font-[350] text-zinc-400">
						<Icon iconNode={coinsStack} className="size-3" />
						{numImages * model.credits} credits
					</p>
					<SubmitButton
						variant="secondary"
						className="w-full cursor-pointer bg-indigo-500 hover:bg-indigo-500/80"
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						{...{ disabled: submitting || !prompt.trim() || (imageToImage && !image) }}
						data-umami-event={imageToImage ? CLICK_GEN_IMAGE_TO_IMAGE : CLICK_GEN_TEXT_TO_IMAGE}
						data-umami-event-model={model.id}
					>
						Generate
					</SubmitButton>
				</div>
			</div>

			<div
				className={cn(
					"flex min-h-64 w-full items-start justify-center overflow-y-auto rounded bg-zinc-800 p-4 md:h-[90dvh]",
					"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
				)}
			>
				<div className="flex w-full flex-col gap-4 lg:gap-8">
					<div className="flex w-full flex-col">
						{previewImageBase64s && (
							<div
								className={cn(
									"grid w-full grid-cols-2 gap-1 xl:grid-cols-4",
									// previewImageBase64s.length === 1 && "grid-cols-1",
									// previewImageBase64s.length >= 2 && "grid-cols-2",
								)}
							>
								{previewImageBase64s.map((base64, index) => (
									<div key={index} className="group relative aspect-square w-full rounded border bg-zinc-900/50">
										{base64 ? (
											<div
												className="h-full w-full rounded bg-contain bg-center bg-no-repeat"
												style={{ backgroundImage: `url(${base64})` }}
												role="img"
												aria-label={`Generated image ${index + 1}`}
											></div>
										) : (
											submitting && (
												<p className="flex h-full w-full flex-col items-center justify-center text-center text-zinc-400">
													<LoaderCircle className="size-6 animate-spin" />
													<span className="text-sm tabular-nums">{seconds}s</span>
												</p>
											)
										)}
										<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
											{base64 && (
												<Hint label="Download image" sideOffset={10}>
													<div className="relative">
														<SubmitButton
															isSubmitting={downloadingImageIndex === index}
															disabled={!base64}
															size="icon"
															variant="secondary"
															className="cursor-pointer"
															onClick={async () => {
																try {
																	setDownloadingImageIndex(index);
																	if (userHasPaid) {
																		await downloadImageFromBase64(base64);
																	} else {
																		await downloadImageFromBase64WithWatermark(base64);
																	}
																} catch (error) {
																	console.error("Failed to download image:", error);
																} finally {
																	setDownloadingImageIndex(null);
																}
															}}
														>
															<Download />
														</SubmitButton>
													</div>
												</Hint>
											)}
										</div>
									</div>
								))}
							</div>
						)}
					</div>
				</div>
			</div>

			<Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
				<DialogContent
					className="flex items-center justify-center overflow-hidden p-0"
					// style={{ width: "auto", height: "auto", maxWidth: "90vw", maxHeight: "90vh" }}
				>
					<img src={dialogUrl} className="object-contain" alt="" style={{ maxWidth: "100%", maxHeight: "100%", width: "auto", height: "auto" }} />
				</DialogContent>
			</Dialog>
		</div>
	);
}
