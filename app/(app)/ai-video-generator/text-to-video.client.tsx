"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Download, Icon, ChevronDownIcon, Check, Clock, Clock12 } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { calculateProgress, cn } from "@/lib/utils";
import { coinsStack } from "@lucide/lab";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { downloadImageFromUrl } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { Hint } from "@/components/ui/custom/hint";
import Link from "next/link";
import { textToVideoModels, VideoModel, getAspectRatioIcon, getAspectRatioClass, getVideoModelCredits } from "@/lib/utils-video-model";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { WEBNAME } from "@/lib/constants";
import { Progress } from "@/components/ui/custom/progress";
import { MediaResultStatus } from "@/@types/media/media-type";
import { CLICK_GEN_TEXT_TO_VIDEO } from "@/lib/umami-event-name";

const isAspectRatioExist = (model: VideoModel, aspectRatio: string) => {
	if (model.aspectRatioAll && model.aspectRatioAll.includes(aspectRatio)) {
		return true;
	}
	return false;
};
const isDurationExist = (model: VideoModel, duration: number) => {
	if (model.durationAll && model.durationAll.includes(duration)) {
		return true;
	}
	return false;
};

const formatTakenTime = (seconds: number) => {
	if (seconds <= 100) {
		return `${Math.floor(seconds)} sec`;
	}
	return `${Math.floor(seconds / 60)} min`;
};

export default function TextToVideo() {
	const searchParams = useSearchParams();
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	useEffect(() => {
		if (user && !userHasPaid) {
			setPlanBoxOpen(true);
		}
	}, []);

	const modelParam = searchParams.get("model");
	const initialModel = modelParam ? textToVideoModels.find((m) => m.id === modelParam) || textToVideoModels[0] : textToVideoModels[0];
	const [model, setModel] = useState<VideoModel>(initialModel);
	const [modelStyle, setModelStyle] = useState<string | null>(initialModel.modelStyle ? initialModel.modelStyle[0].id : null);
	const [prompt, setPrompt] = useState<string>("");
	const [aspectRatio, setAspectRatio] = useState<string>(initialModel.aspectRatioAll ? initialModel.aspectRatioAll[0] : "16:9");
	const [resolution, setResolution] = useState<string | null>(initialModel.resolutionAll ? initialModel.resolutionAll[0] : null);
	const [duration, setDuration] = useState<number>(initialModel.durationAll ? initialModel.durationAll[0] : 3);

	const [paramsInfo, setParamsInfo] = useState<{
		model: VideoModel;
		aspectRatioClass: string;
	}>({
		model: initialModel,
		aspectRatioClass: getAspectRatioClass(initialModel.aspectRatioAll ? initialModel.aspectRatioAll[0] : "16:9"),
	});
	const [previewVideo, setPreviewVideo] = useState<string | null>(null);
	// const [previewVideo, setPreviewVideo] = useState<string | null>(
	// 	"https://videocdn.pollo.ai/web-cdn/pollo/production/cmatg0lee0dulm5azhx6vajaz/video/1749178015507-038828b0-3441-4715-8ec2-4fa894252929.mp4",
	// );

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateVideo = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		setParamsInfo({
			model: model,
			aspectRatioClass: getAspectRatioClass(aspectRatio),
		});

		try {
			setPreviewVideo(null);
			setSubmitting(true);

			// request task
			const { status, message, task_status, request_id } = await ofetch("/api/v1/video/generate-video", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					model: model.id,
					prompt: promtpTrim,
					aspectRatio: aspectRatio,
					duration: duration,
					resolution: resolution,
					modelStyle: modelStyle,
				},
			});
			handleError(status, message);
			refreshUser();

			let taskStatus = MediaResultStatus.InProgress;
			let taskError = null;
			await new Promise((resolve) => setTimeout(resolve, (paramsInfo.model.time ?? 60) * 1000));

			// get task status
			while (taskStatus !== MediaResultStatus.Completed && taskStatus !== MediaResultStatus.Failed) {
				await new Promise((resolve) => setTimeout(resolve, 7500)); // wait for 7.5 seconds
				let {
					status: request_status,
					message: request_message,
					taskStatus: reuqest_taskStatus,
					taskError: request_taskError,
					resultUrls,
				} = await ofetch("/api/v1/video/status", {
					method: "POST",
					body: { id: request_id },
				});
				handleError(request_status, request_message);
				taskStatus = reuqest_taskStatus;
				taskError = request_taskError;
				if (resultUrls[0]) {
					setPreviewVideo(resultUrls[0]);
				}
			}
			if (taskStatus === MediaResultStatus.Failed) {
				if (taskError) {
					throw new Error(taskError);
				}
				throw new Error("Generate video failed. Please try again or contact support.");
			}

			// setPreviewVideo(
			// 	"https://videocdn.pollo.ai/web-cdn/pollo/production/cmatg0lee0dulm5azhx6vajaz/video/1749178015507-038828b0-3441-4715-8ec2-4fa894252929.mp4",
			// );
		} catch (error: any) {
			setPreviewVideo(null);
			console.error("Failed to generate video:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message || "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState(false);

	return (
		<div className="flex min-h-svh w-full flex-col gap-4 overflow-hidden bg-zinc-900 px-4 py-4 md:flex-row">
			<div className="flex w-full shrink-0 flex-col rounded border border-zinc-700 bg-zinc-800 md:h-[90dvh] md:max-w-80 md:min-w-80 xl:max-w-[360px] xl:min-w-[360px]">
				<div className="flex flex-row items-center gap-3 border-b border-zinc-700 px-3 pt-2 pb-2 text-sm text-zinc-100 lg:px-4 lg:pt-3">
					<Link href="/ai-video-generator" className={cn("font-[350]", "pointer-events-none underline decoration-2 underline-offset-12")}>
						Text to Video
					</Link>
					<Link href="/image-to-video" className={cn("font-[350]", "text-zinc-400 hover:text-zinc-300")}>
						Image to Video
					</Link>
				</div>
				<div
					className={cn(
						"grow space-y-4 overflow-y-auto p-3 lg:p-4",
						"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
					)}
				>
					<div className="space-y-[6px]">
						<p className="text-xs text-zinc-300">Model</p>
						<DropdownMenu>
							<DropdownMenuTrigger
								className={cn(
									"ring-offset-background flex h-9 w-full cursor-pointer flex-row items-center justify-between rounded-md border border-zinc-700 bg-zinc-900/50 px-3 py-2 text-sm whitespace-nowrap text-zinc-200 shadow-none ring-offset-0 hover:bg-zinc-800",
									"focus:ring-ring data-placeholder:text-muted-foreground focus:ring-0 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								)}
							>
								<div className="flex flex-row items-center gap-2">
									<img className="size-[14px]" src={model.logo} alt="" />
									<span className="text-[13px] font-[350]">{model.name}</span>
								</div>
								<ChevronDownIcon className="-me-1 opacity-60" size={16} aria-hidden="true" />
							</DropdownMenuTrigger>
							<DropdownMenuContent className="min-w-(--radix-dropdown-menu-trigger-width) border-zinc-700 bg-zinc-900">
								{textToVideoModels.map((modelOption, index) => (
									<DropdownMenuItem
										key={index}
										className="flex cursor-pointer flex-row items-center justify-between gap-2 text-[13px] font-[350] text-zinc-300 focus:bg-indigo-500 focus:text-zinc-200 [&>svg]:size-[14px]"
										onClick={() => {
											setModel(modelOption);
											if (!isAspectRatioExist(modelOption, aspectRatio)) {
												setAspectRatio(modelOption.aspectRatioAll ? modelOption.aspectRatioAll[0] : "16:9");
											}
											if (!isDurationExist(modelOption, duration)) {
												setDuration(modelOption.durationAll ? modelOption.durationAll[0] : 3);
											}
											if (modelOption.resolutionAll) {
												setResolution(modelOption.resolutionAll[0]);
											}
										}}
									>
										<div className="flex flex-row items-center gap-2">
											<img className="size-[14px] text-white" src={modelOption.logo} alt="" />
											<span className="">{modelOption.name}</span>
											{modelOption.time && (
												<p className="flex flex-row items-center gap-0.5 rounded border border-zinc-500 px-[3px] py-px text-[10px]">
													<Clock12 className="size-[10px]" /> {formatTakenTime(modelOption.time)}
												</p>
											)}
										</div>
										{modelOption.id === model.id && <Check className="size-[14px]" />}
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<div className="space-y-[6px]">
						<p className="text-xs text-zinc-300">Prompt</p>
						<Textarea
							placeholder="Describe your video scene..."
							rows={5}
							maxLength={2000}
							className={cn(
								"max-h-48 min-h-32",
								"resize-none border-zinc-700 text-zinc-200 shadow-none focus-visible:shadow-sm focus-visible:ring-0 dark:bg-zinc-900/50",
								"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
							)}
							value={prompt}
							onChange={(e) => setPrompt(e.target.value)}
						/>
					</div>
					{model.aspectRatioAll && (
						<div className="space-y-[6px]">
							<p className="text-xs text-zinc-300">Aspect ratio</p>
							<div
								className={cn("grid gap-1 text-sm font-[350] text-zinc-200", model.aspectRatioAll.length === 4 ? `grid-cols-4` : "grid-cols-3")}
							>
								{model.aspectRatioAll.map((aspectRatioOption, index) => {
									const IconComponent = getAspectRatioIcon(aspectRatioOption);
									return (
										<button
											key={index}
											onClick={() => setAspectRatio(aspectRatioOption)}
											className={cn(
												"flex h-8 w-full cursor-pointer flex-row items-center gap-2 rounded border border-zinc-700 px-3",
												aspectRatio === aspectRatioOption && "border-indigo-500",
											)}
										>
											<IconComponent className="size-4" />
											<span className="text-[13px]">{aspectRatioOption}</span>
										</button>
									);
								})}
							</div>
						</div>
					)}
					{model.durationAll && (
						<div className="space-y-[6px]">
							<p className="text-xs text-zinc-300">Duration</p>
							<div className={cn("grid gap-1 text-sm font-[350] text-zinc-200", model.durationAll.length === 4 ? `grid-cols-4` : "grid-cols-3")}>
								{model.durationAll.map((durationOption, index) => (
									<button
										key={index}
										onClick={() => setDuration(durationOption)}
										className={cn(
											"flex h-8 w-full cursor-pointer flex-row items-center gap-2 rounded border border-zinc-700 px-3",
											duration === durationOption && "border-indigo-500",
										)}
									>
										<Clock className="size-3 text-zinc-300" />
										<span className="text-[13px]">{durationOption}s</span>
									</button>
								))}
							</div>
						</div>
					)}

					{model.resolutionAll && (
						<div className="space-y-[6px]">
							<p className="text-xs text-zinc-300">Resolution</p>
							<div
								className={cn("grid gap-1 text-sm font-[350] text-zinc-200", model.resolutionAll.length === 4 ? `grid-cols-4` : "grid-cols-3")}
							>
								{model.resolutionAll.map((resolutionOption, index) => (
									<button
										key={index}
										onClick={() => setResolution(resolutionOption)}
										className={cn(
											"flex h-8 w-full cursor-pointer flex-row items-center gap-2 rounded border border-zinc-700 px-3",
											resolution === resolutionOption && "border-indigo-500",
										)}
									>
										<span className="text-[13px]">{resolutionOption}</span>
									</button>
								))}
							</div>
						</div>
					)}

					{model.modelStyle && (
						<div className="space-y-[6px]">
							<p className="text-xs text-zinc-300">Style</p>
							<div className={cn("grid gap-1 text-sm font-[350] text-zinc-200", model.modelStyle.length === 4 ? "grid-cols-4" : "grid-cols-3")}>
								{model.modelStyle.map((modelStyleOption, index) => (
									<button
										key={index}
										onClick={() => setModelStyle(modelStyleOption.id)}
										className={cn(
											"flex h-8 w-full cursor-pointer flex-row items-center gap-2 rounded border border-zinc-700 px-3",
											modelStyle === modelStyleOption.id && "border-indigo-500",
										)}
									>
										<span className="text-[13px] leading-3">{modelStyleOption.name}</span>
									</button>
								))}
							</div>
						</div>
					)}
				</div>

				<div className="space-y-0.5 border-t border-zinc-700 p-3 lg:p-4">
					<p className="flex flex-row items-center gap-1 text-[11px] font-[350] text-zinc-400">
						<Icon iconNode={coinsStack} className="size-3" />
						{getVideoModelCredits(model, duration, resolution)} credits
						{/* {model.credits * (model.durationAll ? duration : 1)} credits */}
					</p>
					<SubmitButton
						variant="secondary"
						className="w-full cursor-pointer bg-indigo-500 hover:bg-indigo-500/80"
						isSubmitting={submitting}
						onClick={handleGenerateVideo}
						{...{ disabled: submitting || !prompt.trim() }}
						data-umami-event={CLICK_GEN_TEXT_TO_VIDEO}
						data-umami-event-model={model.id}
					>
						Generate
					</SubmitButton>
				</div>
			</div>

			<div
				className={cn(
					"min-h-64 w-full items-start justify-center overflow-y-hidden rounded bg-zinc-800 p-4 md:h-[90dvh]",
					"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
				)}
			>
				<div className="">
					<div className="w-full">
						{submitting ? (
							<div className="mx-auto flex min-h-56 w-full min-w-full shrink-0 rounded border border-zinc-700 bg-zinc-900/50 sm:aspect-video sm:max-w-[512px] sm:min-w-[512px] md:max-w-full md:min-w-full lg:max-w-[512px] lg:min-w-[512px]">
								<div className="mx-auto flex flex-col items-center justify-center gap-2 px-4 text-center text-zinc-300">
									{/* <LoaderCircle className="size-6 animate-spin" /> */}
									<span className="font-mono text-sm tabular-nums">{seconds}s</span>
									<div className="flex flex-row items-center gap-1 text-xs">
										<Progress
											value={calculateProgress(Number(seconds), paramsInfo.model.time ?? 60)}
											className="h-1.5 w-[280px] bg-zinc-500"
											indicatorClassName="bg-indigo-600"
										/>
										<span className="font-mono tabular-nums">{calculateProgress(Number(seconds), paramsInfo.model.time ?? 60)}%</span>
									</div>
									<div className="text-xs font-[350]">
										{WEBNAME} is generating your video, which may take {paramsInfo.model.time} seconds. Once finished, the video will be
										saved in{" "}
										<Link href="/my-creations" target="_blank" className="text-indigo-500 hover:underline hover:underline-offset-4">
											My Creations
										</Link>
										.
									</div>
								</div>
							</div>
						) : (
							previewVideo && (
								<div className={cn("group relative mx-auto w-full max-w-[512px]")}>
									<video
										src={previewVideo}
										muted
										controls
										controlsList="nodownload noplaybackrate"
										disableRemotePlayback
										className="h-full w-full rounded object-contain"
										onContextMenu={(e) => e.preventDefault()}
										onDragStart={(e) => e.preventDefault()}
										onMouseEnter={(e) => (e.target as HTMLVideoElement).play()}
										// onMouseLeave={(e) => (e.target as HTMLVideoElement).pause()}
									/>
									{/* <MediaController className="">
										<video slot="media" src={previewVideo} preload="auto" muted crossOrigin="" />
										<MediaControlBar>
											<MediaPlayButton></MediaPlayButton>
											<MediaMuteButton></MediaMuteButton>
											<MediaTimeRange></MediaTimeRange>
											<MediaTimeDisplay remaining></MediaTimeDisplay>
											<MediaPipButton></MediaPipButton>
											<MediaFullscreenButton></MediaFullscreenButton>
										</MediaControlBar>
									</MediaController> */}
									<div className="absolute top-2 right-2 z-10 items-center gap-1">
										{previewVideo && (
											<Hint label="Download" sideOffset={10}>
												<div className="relative">
													<SubmitButton
														isSubmitting={downloading}
														disabled={!previewVideo}
														size="icon"
														variant="secondary"
														className="cursor-pointer"
														onClick={async () => {
															try {
																setDownloading(true);
																if (userHasPaid) {
																	await downloadImageFromUrl(previewVideo);
																} else {
																	setPlanBoxOpen(true);
																}
															} catch (error) {
																console.error("Failed to download:", error);
															} finally {
																setDownloading(false);
															}
														}}
													>
														<Download />
													</SubmitButton>
												</div>
											</Hint>
										)}
									</div>
								</div>
							)
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
