"use client";

import { ChevronDown, CreditCard, Icon, Power, MenuIcon, GalleryHorizontalEndIcon, MessageCircle, InboxIcon, MailIcon, CoinsIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useSidebar } from "@/components/ui/sidebar";
import { useSession } from "@/lib/auth-client";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignOut } from "@/hooks/use-signout";
import { useUserStore } from "@/store/useUserStore";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { Logo } from "@/components/logo";
import { EMAIL_CONTACT, WEBNAME } from "@/lib/constants";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { coinsStack } from "@lucide/lab";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

export function AppSiteHeader() {
	const { isMobile, toggleSidebar } = useSidebar();
	const { data: session } = useSession();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const { handleSignOut } = useSignOut();
	const { user, creditsAll: userCreditsAll, hasPaid: userHasPaid } = useUserStore();

	return (
		<header className="sticky top-0 z-50 flex w-full items-center border-b bg-zinc-900">
			<div className="flex h-(--header-height) w-full items-center gap-4 px-4">
				{isMobile && (
					<Button variant="ghost" size="icon" onClick={toggleSidebar} className="h-6 w-6 cursor-pointer">
						<MenuIcon className="size-[22px]" />
					</Button>
				)}

				<div className="flex w-full flex-row items-center justify-between gap-2">
					<NoPrefetchLink href="/" className="flex items-center gap-2 rtl:space-x-reverse">
						<Logo className="size-8 rounded border-zinc-700" />
						<span className="hidden text-base font-medium text-zinc-100 md:block">{WEBNAME}</span>
					</NoPrefetchLink>

					<div className="flex flex-row items-center gap-2">
						{session ? (
							<>
								{!userHasPaid && (
									<Button
										size="sm"
										variant="secondary"
										className="cursor-pointer bg-indigo-500 text-[13px] font-normal hover:bg-indigo-500/80"
										onClick={() => setPlanBoxOpen(true)}
									>
										{/* Get a plan */}
										Upgrade
									</Button>
								)}
								<div className="flex flex-row items-center gap-1 text-xs font-[350]">
									{/* <Icon iconNode={coinsStack} className="size-3 text-green-500" />{" "} */}
									<CoinsIcon className="size-3.5 text-green-500" /> <span>{userCreditsAll}</span>
								</div>
								{/* {user?.membershipId === MembershipID.Free && (
								<button className="relative flex items-center gap-2 text-xs" onClick={() => setPlanBoxOpen(true)}>
									<Crown className="size-3.5 fill-current text-yellow-500" />
									Upgrade
								</button>
							)} */}
								<DropdownMenu modal={false}>
									<DropdownMenuTrigger asChild className="cursor-pointer">
										<div className="flex shrink-0 flex-row items-center gap-2">
											<Avatar className="size-6">
												<AvatarImage src={session?.user.image!} alt="User Avatar" />
												<AvatarFallback>{session?.user.name}</AvatarFallback>
											</Avatar>
											<ChevronDown className="size-3.5 shrink-0 text-neutral-600 lg:block" />
										</div>
									</DropdownMenuTrigger>
									<DropdownMenuContent className="w-[280px] border-zinc-700 bg-zinc-800 p-0 text-zinc-300" align="end" forceMount>
										<div className="flex gap-5 p-5">
											<Avatar className="flex size-9 shrink-0 items-center gap-2 text-neutral-800">
												<AvatarImage src={session?.user.image!} alt="User Avatar" />
												<AvatarFallback>{session?.user.name}</AvatarFallback>
											</Avatar>
											<div className="flex min-w-0 flex-1 flex-col items-start">
												<p className="truncate text-sm font-semibold">{session?.user.name ?? WEBNAME}</p>
												<p className="text-muted-foreground mt-1 truncate text-xs">{session?.user.email ?? "--"}</p>
											</div>
										</div>
										{/* {user?.membershipId === MembershipID.Free && (
										<div className="px-[24px] pb-5">
											<Button size="sm" className="w-full bg-blue-500 hover:bg-blue-600" onClick={() => setPlanBoxOpen(true)}>
												Get a plan
											</Button>
										</div>
									)} */}

										<div className="flex flex-row items-center justify-between px-[24px] pb-4">
											<div className="flex flex-row items-center gap-1 text-sm font-[350]">
												{/* <Icon iconNode={coinsStack} className="size-4 text-green-500" />{" "} */}
												<CoinsIcon className="size-3.5 text-green-500" />
												{userCreditsAll} credits
												{/* <SubmitButton
												isSubmitting={refreshingUserCredits}
												variant="ghost"
												size="icon"
												className="h-6 w-6 hover:text-indigo-500"
												onClick={() => handleRefreshUserCredits()}
											>
												<RefreshCw className="size-4" />
											</SubmitButton> */}
											</div>
											<Button
												size="sm"
												variant="secondary"
												className="cursor-pointer bg-indigo-500 hover:bg-indigo-600"
												onClick={() => setPlanBoxOpen(true)}
											>
												Get more
											</Button>
										</div>
										<Separator className="bg-zinc-700" />

										<NoPrefetchLink
											href="/my-creations"
											className="flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100 hover:bg-zinc-700 hover:text-zinc-200"
										>
											<div className="flex flex-row items-center">
												<GalleryHorizontalEndIcon className="mr-5 h-4 w-4 shrink-0" />
												My Creations
											</div>
										</NoPrefetchLink>
										<NoPrefetchLink
											href="/user/my-billing"
											className="flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100 hover:bg-zinc-700 hover:text-zinc-200"
										>
											<div className="flex flex-row items-center">
												<CreditCard className="mr-5 h-4 w-4 shrink-0" />
												My Billing
											</div>
											{/* <p className="flex items-center gap-1 rounded bg-muted px-2 py-1 text-xs font-medium">{user?.membershipFormatted}</p> */}
										</NoPrefetchLink>
										<a
											href={`mailto:${EMAIL_CONTACT}`}
											target="_blank"
											className="flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100 hover:bg-zinc-700 hover:text-zinc-200"
										>
											<div className="flex flex-row items-center">
												<MailIcon className="mr-5 h-4 w-4 shrink-0" />
												Contact Us
											</div>
										</a>

										<Separator className="bg-zinc-700" />

										<button
											className="flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100 hover:bg-zinc-700 hover:text-zinc-200"
											onClick={handleSignOut}
										>
											<Power className="mr-5 h-4 w-4 shrink-0" />
											Sign out
										</button>
									</DropdownMenuContent>
								</DropdownMenu>
							</>
						) : (
							<Button size="sm" onClick={() => setSignInBoxOpen(true)} className="cursor-pointer rounded-full">
								Sign In
							</Button>
						)}
					</div>
				</div>
			</div>
		</header>
	);
}
