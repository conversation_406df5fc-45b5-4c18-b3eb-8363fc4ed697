"use client";

import { useState } from "react";
import { toast } from "sonner";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { ofetch } from "ofetch";
import { AuthError, IgnoreError, Credits402Error, handleError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Download, Icon, LoaderCircle, Loader2, ImagePlus, Trash, Sparkles } from "lucide-react";
import { cn } from "@/lib/utils";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { coinsStack } from "@lucide/lab";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, fileToBase64, imageUrlToBase64 } from "@/lib/file/utils-file";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { Hint } from "@/components/ui/custom/hint";
import { Dropzone, DropzoneEmptyState } from "@/components/ui/kibo-ui/dropzone/index";
import { buttonVariants } from "@/components/ui/button";
import { uploadFile } from "@/lib/file/upload-file";
import { Comparison, ComparisonHandle, ComparisonItem } from "@/components/ui/kibo-ui/comparison";
import { OSS_URL_HOST } from "@/lib/constants";

export default function ImageUpscaler() {
	const { data: session } = useSession();
	const { refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [showDemo, setShowDemo] = useState(true);
	const [image, setImage] = useState<{
		base64: string;
		url: string;
	} | null>(null);
	const [previewImageBase64, setPreviewImageBase64] = useState<string | null>(null);
	// const [image, setImage] = useState<{
	// 	base64: string;
	// 	url: string;
	// } | null>({
	// 	base64: "https://static.youstylize.com/dev/image_result/202504/2025040701960f88c96272eb9f49f2e5e5ea7c0f.png",
	// 	url: "https://static.youstylize.com/dev/image_result/202504/2025040701960f88c96272eb9f49f2e5e5ea7c0f.png",
	// });
	// const [previewImageBase64, setPreviewImageBase64] = useState<string | null>(
	// 	"https://static.youstylize.com/dev/image_result/202504/2025040701960f88c96272eb9f49f2e5e5ea7c0f.png",
	// );

	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const handleLocalFileDrop = async (files: File[]) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		if (!files || files.length === 0) return;

		try {
			setUploadingImage(true);
			const { file_url } = await uploadFile(files[0]);
			// const file_url = "";
			const base64 = await fileToBase64(files[0]);
			setImage({
				url: file_url,
				base64,
			});
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		setShowDemo(false);

		try {
			setPreviewImageBase64(null);
			setSubmitting(true);
			const { status, message, resultUrl } = await ofetch("/api/v1/image/image-upscaler", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: {
					image: image?.url,
				},
			});
			handleError(status, message);
			refreshUser();

			// const resultUrl = "/static/images/demo.jpg";

			if (resultUrl) {
				const base64 = await imageUrlToBase64(resultUrl as string);
				setPreviewImageBase64(base64);
			}

			toast.success("Generate success.");
		} catch (error: any) {
			setPreviewImageBase64(null);
			console.error("Failed to generate image:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error(error.message ?? "Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [isImageDialogOpen, setIsImageDialogOpen] = useState<boolean>(false);
	const [dialogUrl, setDialogUrl] = useState<string>("");

	const [downloadingImage, setDownloadingImage] = useState<boolean>(false);

	return (
		<div className="flex min-h-svh w-full flex-col gap-4 overflow-hidden bg-zinc-900 px-4 py-4 md:flex-row">
			<div className="flex w-full shrink-0 flex-col overflow-y-auto rounded border border-zinc-700 bg-zinc-800 md:h-[90dvh] md:max-w-80 md:min-w-80 xl:max-w-[360px] xl:min-w-[360px]">
				<div className="grow space-y-4 p-3 lg:p-4">
					<div className="space-y-[6px]">
						<p className="text-xs text-zinc-300">Image</p>

						{image ? (
							<div className="group relative h-[144px] w-full rounded border border-zinc-700 bg-zinc-900/50 p-0 hover:bg-zinc-900/50">
								<img
									src={image.base64}
									alt="Model"
									className="h-[144px] w-full object-contain"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
								<div className="absolute inset-0 hidden h-[144px] items-center justify-center bg-black/50 group-hover:flex">
									<button
										onClick={() => {
											setImage(null);
											setPreviewImageBase64(null);
										}}
										className="cursor-pointer rounded-full p-2 text-zinc-300/80 transition-colors hover:text-white"
									>
										<Trash className="size-4" />
									</button>
								</div>
							</div>
						) : (
							<div
								onClick={() => {
									if (!session) {
										setSignInBoxOpen(true);
									}
								}}
							>
								<Dropzone
									multiple={false}
									maxFiles={1}
									noClick={!session}
									onDragEnter={() => {
										if (!session) {
											setSignInBoxOpen(true);
											return;
										}
									}}
									onDrop={(files) => handleLocalFileDrop(files)}
									accept={{
										"image/jpeg": [".jpg", ".jpeg", ".png", ".webp"],
									}}
									onError={console.error}
									className="h-[144px] w-full cursor-pointer border p-0 dark:bg-zinc-900/50 dark:hover:bg-zinc-800"
								>
									<DropzoneEmptyState>
										<>
											{uploadingImage ? (
												<Loader2 className="animate-spin text-zinc-400" />
											) : (
												<div className="text-muted-foreground flex h-10 flex-col items-center gap-2 font-normal hover:text-zinc-400">
													<ImagePlus />
													<p>Click to upload an image</p>
												</div>
											)}
											{/* <p className="w-full text-sm font-normal text-muted-foreground">Or drop an image</p> */}
										</>
									</DropzoneEmptyState>
								</Dropzone>
							</div>
						)}
					</div>
				</div>

				<div className="space-y-0.5 border-t border-zinc-700 p-3 lg:p-4">
					<p className="text-muted-foreground flex flex-row items-center gap-1 text-[11px] font-[350]">
						<Icon iconNode={coinsStack} className="size-3" />1 credits
					</p>
					<SubmitButton
						variant="secondary"
						className="w-full cursor-pointer bg-indigo-500 hover:bg-indigo-500/80"
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						{...{ disabled: submitting || !image }}
					>
						<Sparkles />
						Upscale
					</SubmitButton>
				</div>
			</div>

			<div
				className={cn(
					"flex min-h-64 w-full items-start justify-center overflow-y-hidden rounded bg-zinc-800 p-4 md:h-[90dvh]",
					"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
				)}
			>
				<div className="h-full w-full">
					{showDemo ? (
						<div className="mx-auto flex h-full max-w-2xl flex-col justify-center space-y-4">
							<p className="text-sm text-zinc-300">Sample Image</p>
							<div className="mx-auto flex w-full max-w-3xl rounded border border-zinc-700 bg-zinc-900/50">
								<Comparison className="aspect-[3/2] max-w-3xl bg-zinc-900/50">
									<ComparisonItem position="right" className="">
										<div className="relative">
											<img
												src={`${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/image-upscaler-sample-before.avif`}
												alt="Image Upscaler Sample Before"
												className="h-full w-full rounded object-contain"
												onContextMenu={(e) => e.preventDefault()}
												onDragStart={(e) => e.preventDefault()}
											/>
											<div
												className="absolute top-2 left-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
												style={{ "--content": "'Before'" } as React.CSSProperties}
											></div>
										</div>
									</ComparisonItem>
									<ComparisonItem position="left" className="">
										<div className="relative">
											<img
												src={`${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/image-upscaler-sample-after.avif`}
												alt="Image Upscaler Sample After"
												className="h-full w-full rounded object-contain"
												onContextMenu={(e) => e.preventDefault()}
												onDragStart={(e) => e.preventDefault()}
											/>
											<div
												className="absolute top-2 right-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
												style={{ "--content": "'After'" } as React.CSSProperties}
											></div>
										</div>
									</ComparisonItem>
									<ComparisonHandle />
								</Comparison>
							</div>
						</div>
					) : (
						<div className="mx-auto flex h-full max-w-3xl flex-col justify-center space-y-4">
							<div
								className={cn(
									"group relative mx-auto flex w-full max-w-3xl rounded",
									(previewImageBase64 || submitting) && "border border-zinc-700 bg-zinc-900/50",
								)}
							>
								{previewImageBase64 && image && (
									<>
										<Comparison className="aspect-[3/2] max-w-3xl">
											<ComparisonItem position="right" className="">
												<div className="relative">
													<div
														className="aspect-[3/2] h-full w-full rounded bg-contain bg-center bg-no-repeat"
														style={{ backgroundImage: `url(${image.base64})` }}
														role="img"
														aria-label="Original image"
													></div>
													<div
														className="absolute top-2 left-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
														style={{ "--content": "'Before'" } as React.CSSProperties}
													></div>
												</div>
											</ComparisonItem>
											<ComparisonItem position="left" className="">
												<div className="relative">
													<div
														className="aspect-[3/2] h-full w-full rounded bg-contain bg-center bg-no-repeat"
														style={{ backgroundImage: `url(${previewImageBase64})` }}
														role="img"
														aria-label="Upscaled image"
													></div>
													<div
														className="absolute top-2 right-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
														style={{ "--content": "'After'" } as React.CSSProperties}
													></div>
												</div>
											</ComparisonItem>
											<ComparisonHandle />
										</Comparison>
										<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
											{previewImageBase64 && (
												<Hint label="Download image" sideOffset={10}>
													<div className="relative">
														<SubmitButton
															isSubmitting={downloadingImage}
															disabled={!previewImageBase64}
															size="icon"
															onClick={async () => {
																try {
																	setDownloadingImage(true);
																	if (userHasPaid) {
																		await downloadImageFromBase64(previewImageBase64);
																	} else {
																		await downloadImageFromBase64WithWatermark(previewImageBase64);
																	}
																} catch (error) {
																	console.error("Failed to download image:", error);
																} finally {
																	setDownloadingImage(false);
																}
															}}
														>
															<Download />
														</SubmitButton>
													</div>
												</Hint>
											)}
										</div>
									</>
								)}
								{submitting && (
									<p className="flex aspect-[3/2] h-full w-full max-w-3xl flex-col items-center justify-center text-center text-zinc-400">
										<LoaderCircle className="size-6 animate-spin" />
										<span className="text-sm tabular-nums">{seconds}s</span>
									</p>
								)}
							</div>
						</div>
					)}
				</div>
			</div>

			<Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
				<DialogContent
					className="flex items-center justify-center overflow-hidden p-0"
					// style={{ width: "auto", height: "auto", maxWidth: "90vw", maxHeight: "90vh" }}
				>
					<img src={dialogUrl} className="object-contain" alt="" style={{ maxWidth: "100%", maxHeight: "100%", width: "auto", height: "auto" }} />
				</DialogContent>
			</Dialog>
		</div>
	);
}
