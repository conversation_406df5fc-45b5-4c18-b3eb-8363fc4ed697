import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import ImageUpscaler from "./image-upscaler.client";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { Comparison, ComparisonHandle, ComparisonItem } from "@/components/ui/kibo-ui/comparison";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { HowToUse } from "@/components/landing/how-to-use";
import FeaturesComparisonComponent from "@/components/landing/features-c";

export const metadata: Metadata = {
	title: `AI Image Upscaler: Enhance Photo to 4K Online | ${WEBNAME}`,
	description:
		"Dreampik's AI image upscaler enhance your photo quality to 4K, sharpen details, and restore resolution. Try our advanced ai image upscaler free now!",
	alternates: {
		canonical: "/image-upscaler",
	},
};

export default async function Page() {
	return (
		<main className="">
			<div className="">
				<ImageUpscaler />
			</div>

			<div className="relative pt-20 pb-20">
				<div className="mx-auto max-w-5xl px-6">
					<div className="mt-8 space-y-6 text-center sm:mx-auto lg:mt-16 lg:mr-auto">
						<h1 className="mx-auto max-w-4xl text-5xl font-semibold">AI Image Upscaler – Upscale image quality with AI online</h1>
						<div className="text-muted-foreground mx-auto mt-4 md:text-lg">
							Enhance your photos in seconds with our AI image upscaler. Easily upscale images to 4K online—sharpen details, boost resolution, and
							restore quality with one click. Perfect for digital art, social media, e-commerce, and more. Try the smartest way to make your
							pictures look their best.
						</div>
					</div>

					<div className="mt-8 flex flex-col items-center justify-center gap-2 md:flex-row">
						<Link
							href="#"
							className={cn(buttonVariants({ size: "lg" }), `h-12 rounded-full text-base text-nowrap after:content-(--content)`)}
							// style={{ "--content": "'Create Images'" } as React.CSSProperties}
						>
							Use Our Image Upscaler Now
						</Link>
					</div>

					<div className="mx-auto mt-12 flex w-full max-w-4xl rounded">
						<Comparison className="aspect-[3/2] max-w-4xl">
							<ComparisonItem position="right" className="">
								<div className="relative">
									<img
										src={`${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/image-upscaler-sample-before.avif`}
										alt="Image Upscaler Sample Before"
										className="h-full w-full rounded-lg object-contain"
										loading="lazy"
									/>
									<div
										className="absolute top-2 left-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
										style={{ "--content": "'Before'" } as React.CSSProperties}
									></div>
								</div>
							</ComparisonItem>
							<ComparisonItem position="left" className="">
								<div className="relative">
									<img
										src={`${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/image-upscaler-sample-after.avif`}
										alt="Image Upscaler Sample After"
										className="h-full w-full rounded-lg object-contain"
										loading="lazy"
									/>
									<div
										className="absolute top-2 right-2 rounded-sm bg-zinc-800/80 px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
										style={{ "--content": "'After'" } as React.CSSProperties}
									></div>
								</div>
							</ComparisonItem>
							<ComparisonHandle />
						</Comparison>
					</div>
				</div>
			</div>

			<FeaturesComparisonComponent
				ctaText="Upscale Images Now"
				ctaUrl="#"
				features={[
					{
						title: "Upscale Product Photos to Boost Sales",
						description:
							"Make your e-commerce product images stand out in online stores. Sharper, clearer photos help attract customers, improve trust, and increase sales. Our AI image upscaler ensures every detail is crisp, so your products look their best everywhere.",
						beforeSrc: `${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/feature-1-before.avif`,
						afterSrc: `${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/feature-1-after.avif`,
					},
					{
						title: "Restore Old Photos for Digital Archives",
						description:
							"Bring your precious memories back to life. Easily upscale and repair old or damaged photos for digital archives, family albums, or prints. Keep your history safe and share it in high resolution with future generations.",
						beforeSrc: `${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/feature-2-before.avif`,
						afterSrc: `${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/feature-2-after.avif`,
					},
					{
						title: "Enhance Content to Engage Your Audience",
						description:
							"Content creators can quickly upscale images to deliver stunning, high-quality visuals. Better images mean more clicks, shares, and followers—helping your brand and stories make a lasting impact across all platforms.",
						beforeSrc: `${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/feature-3-before.avif`,
						afterSrc: `${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/feature-3-after.avif`,
					},
					{
						title: "Upscale Ad Images to Attract Clients",
						description:
							"Turn ordinary ad visuals into eye-catching graphics. Upscale your advertising images for sharper, more appealing campaigns that grab attention and drive results. Get noticed and win more customers with professional-quality ads.",
						beforeSrc: `${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/feature-4-before.avif`,
						afterSrc: `${OSS_URL_HOST}mkt/pages/image-tools/image-upscaler/feature-4-after.avif`,
					},
				]}
			/>

			<HowToUse
				title="How to Upscale Your Images Online"
				steps={[
					{
						title: "1. Upload Your Image",
						description:
							"Click the upload button to choose your photo. Our AI image upscaler supports all popular formats, making it easy to upscale any image in seconds.",
					},
					{
						title: "2. Upscale image automatically",
						description:
							"Our powerful AI image upscaler automatically enhances and upscales your image to 4K quality. Enjoy sharper details and vibrant colors with advanced AI technology.",
					},
					{
						title: "3. Download and Share",
						description:
							"Download your upscaled image directly to your device. Share your stunning, high-quality photo online or use it for business, social media, or prints.",
					},
				]}
			/>

			<FAQsComponent
				title="AI Image Upscaler Related FAQs"
				faqs={[
					{
						question: "What is an AI image upscaler?",
						answer: "An AI image upscaler is a tool that uses artificial intelligence to increase the resolution of your images. Unlike basic resizing, an AI upscaler adds realistic details, making your photos look sharp and clear even at 4K or higher.",
					},
					{
						question: "What are common uses for an AI image upscaler?",
						answer: "AI image upscalers are often used to boost photo quality for printing, e-commerce, social media, digital art, old photo restoration, and even for making game or animation graphics look sharper. Whether it's product photos or personal memories, AI upscaling helps your images look their best.",
					},
					{
						question: "Why use our image upscaler?",
						answer: "Our AI image upscaler lets you easily upscale images to 4K while preserving details and keeping the original look consistent. You get high resolution and better image quality with just one click—no complex editing required.",
					},
					{
						question: "What types of images can I enhance with AI image upscaler?",
						answer: "You can upscale and enhance most common image formats like JPEG and PNG. The AI image upscaler works for all kinds of pictures, including photos, digital artwork, illustrations, and even screenshots.",
					},
					{
						question: "How do I upscale an image to 4K?",
						answer: "With Dreampik's AI-powered image upscaler, just upload your photo and click a button. In seconds, you'll get a high-quality, 4K version of your image—no technical skills needed.",
					},
					{
						question: "How long does AI image upscaler take?",
						answer: "Our AI image upscaler is very fast. Most images are enhanced and upscaled in just a few seconds.",
					},
					{
						question: "Can AI image upscaler improve the quality of old photos?",
						answer: "Yes! Our AI upscaler can restore and improve the quality of old or low-resolution photos, bringing out more details and making them look new again.",
					},
					{
						question: "Are my uploaded images private and secure?",
						answer: "Absolutely. All images you upload are kept private and secure. Your photos are never shared or used for any other purpose.",
					},
				]}
			/>

			<FinalCTA
				ctaText="Upscale Images Now"
				ctaTextDisplay={true}
				ctaUrl="#"
				ctaClassName="rounded-full"
				title="Upscale Your Images Instantly with AI"
				description="Transform any photo into stunning 4K quality using our AI image upscaler. Enhance details and clarity with just one click—no design skills needed. Experience the power of AI to upscale images online, fast and easy."
			/>
		</main>
	);
}
