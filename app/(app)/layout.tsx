import { AnalyticsClarity } from "@/components/analytics/analytics-clarity";
import { AnalyticsGoolge } from "@/components/analytics/analytics-google";
import Footer from "@/components/navigate/footer";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { AppSiteHeader } from "./app-site-header";
import { AppSidebar } from "./app-sidebar";

export default async function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<>
			<div className="[--header-height:calc(--spacing(14))]">
				<SidebarProvider className="flex flex-col">
					<AppSiteHeader />
					<div className="flex flex-1">
						<AppSidebar />
						<SidebarInset>
							{children}
							<Footer />
						</SidebarInset>
					</div>
				</SidebarProvider>
				<AnalyticsClarity />
				<AnalyticsGoolge />
			</div>
		</>
	);
}
