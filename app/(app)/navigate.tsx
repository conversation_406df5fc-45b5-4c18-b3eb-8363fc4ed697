// "use client";

// import React, { useEffect, useState } from "react";
// import { ChevronDown, CreditCard, Power, Icon, History, CheckIcon } from "lucide-react";
// import { coinsStack } from "@lucide/lab";
// import { <PERSON><PERSON> } from "@/components/ui/button";
// import { Logo } from "@/components/logo";
// import { useSession } from "@/lib/auth-client";
// import { WEBNAME } from "@/lib/constants";
// import { Separator } from "@/components/ui/separator";
// import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
// import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
// import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
// import { useUserStore } from "@/store/useUserStore";
// import { MembershipID } from "@/@types/membership-type";
// import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
// import { useSignOut } from "@/hooks/use-signout";
// import { Select, SelectContent, SelectTrigger, SelectValue } from "@/components/ui/select";
// import { usePathname } from "next/navigation";
// import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

// interface MenuItem {
// 	name: string;
// 	href: string;
// 	href2?: string;
// 	icon?: React.ReactNode;
// }

// const menuItems: MenuItem[] = [
// 	{ name: "Image to Video", href: "/image-to-video" },
// 	{ name: "Text to Video", href: "/ai-video-generator" },
// 	{ name: "Image Generator", href: "/ai-image-generator", href2: "/image-to-image" },
// 	{ name: "image Upscaler", href: "/image-upscaler" },
// ];

// export const HeaderApp = () => {
// 	const pathname = usePathname();
// 	const { data: session } = useSession();
// 	const { setSignInBoxOpen } = useSignInBoxOpenStore();
// 	const { setPlanBoxOpen } = usePlanBoxOpenStore();
// 	const { handleSignOut } = useSignOut();
// 	const { user, refreshUser } = useUserStore();

// 	const [currentItem, setCurrentItem] = useState<MenuItem | null>(menuItems.find((item) => pathname.startsWith(item.href)) ?? null);
// 	const [selectToolOpen, setSelectToolOpen] = useState(false); // ADD THIS LINE

// 	useEffect(() => {
// 		setCurrentItem(menuItems.find((item) => pathname.startsWith(item.href) || (item.href2 && pathname.startsWith(item.href2))) ?? null);
// 	}, [pathname]);

// 	// const [refreshingUserCredits, setRefreshingUserCredits] = useState<boolean>(false);
// 	// const handleRefreshUserCredits = async () => {
// 	// 	if (!session?.user) {
// 	// 		return;
// 	// 	}

// 	// 	try {
// 	// 		setRefreshingUserCredits(true);
// 	// 		await refreshUser();
// 	// 	} catch (error: any) {
// 	// 		console.error(error);
// 	// 		if (error instanceof AuthError) {
// 	// 			setSignInBoxOpen(true);
// 	// 			return;
// 	// 		}

// 	// 		if (error instanceof IgnoreError) {
// 	// 			return;
// 	// 		}
// 	// 		toast.error("Network error");
// 	// 	} finally {
// 	// 		setRefreshingUserCredits(false);
// 	// 	}
// 	// };

// 	return (
// 		<header className="sticky top-0 z-20 w-full border-b border-zinc-700 bg-zinc-900 text-white transition-colors duration-300">
// 			<div className="flex h-14 flex-wrap items-center justify-between px-4">
// 				<div className="flex flex-row items-center gap-2 md:gap-6">
// 					<NoPrefetchLink href="/" className="flex items-center gap-2 rtl:space-x-reverse">
// 						<Logo className="size-8 rounded border-zinc-700" />
// 						<span className="hidden text-lg font-medium text-zinc-100 md:block">{WEBNAME}</span>
// 					</NoPrefetchLink>
// 					{/* {currentItem && pathname.startsWith(currentItem.href) && ( */}
// 					<div className="flex flex-row rounded-lg font-normal">
// 						<Select
// 							open={selectToolOpen}
// 							onOpenChange={(open) => setSelectToolOpen(open)}
// 							value={currentItem?.href}
// 							onValueChange={(value) => setCurrentItem(menuItems.find((item) => item.href === value) ?? null)}
// 						>
// 							<SelectTrigger className="h-8 w-[180px] cursor-pointer border-zinc-700 bg-zinc-900/50 text-zinc-200 shadow-none ring-offset-0 hover:bg-zinc-800 focus:ring-0">
// 								<SelectValue placeholder="Select a tool">
// 									<div className="flex flex-row items-center gap-2">
// 										{/* <Aperture className="h-4 w-4" /> */}
// 										<span className="text-[13px] font-[350] text-zinc-300">{currentItem?.name}</span>
// 									</div>
// 								</SelectValue>
// 							</SelectTrigger>
// 							<SelectContent className="border-zinc-700 bg-zinc-900">
// 								{menuItems.map((item, index) => (
// 									<NoPrefetchLink
// 										key={index}
// 										href={item.href}
// 										className="bg-bzinc-900 flex w-full cursor-pointer flex-row items-center justify-between gap-2 rounded-sm px-1.5 py-1.5 text-[13px] font-[350] text-zinc-300 outline-hidden hover:bg-indigo-500/70 hover:text-zinc-200"
// 										onClick={() => {
// 											setCurrentItem(item);
// 											setSelectToolOpen(false);
// 										}}
// 									>
// 										<span className="">{item.name}</span>
// 										{currentItem?.href === item.href && <CheckIcon className="size-3.5" />}
// 									</NoPrefetchLink>
// 								))}
// 							</SelectContent>
// 						</Select>
// 					</div>
// 					{/* )} */}
// 				</div>

// 				<div className="flex flex-row items-center gap-2">
// 					{session ? (
// 						<>
// 							<Button
// 								variant="ghost"
// 								size="sm"
// 								className="hidden font-[350] text-zinc-300 hover:bg-zinc-800 hover:text-zinc-200 sm:flex"
// 								onClick={() => setPlanBoxOpen(true)}
// 							>
// 								<Icon iconNode={coinsStack} className="text-green-500" /> <span>{(user?.creditOneTime ?? 0) + (user?.creditFree ?? 0)}</span>
// 							</Button>
// 							{/* {user?.membershipId === MembershipID.Free && (
// 								<button className="relative flex items-center gap-2 text-xs" onClick={() => setPlanBoxOpen(true)}>
// 									<Crown className="size-3.5 fill-current text-yellow-500" />
// 									Upgrade
// 								</button>
// 							)} */}
// 							<DropdownMenu modal={false}>
// 								<DropdownMenuTrigger asChild className="cursor-pointer">
// 									<div className="flex shrink-0 flex-row items-center gap-2">
// 										<Avatar className="size-6">
// 											<AvatarImage src={session?.user.image!} alt="User Avatar" />
// 											<AvatarFallback>{session?.user.name}</AvatarFallback>
// 										</Avatar>
// 										<ChevronDown className="size-3.5 shrink-0 text-neutral-600 lg:block" />
// 									</div>
// 								</DropdownMenuTrigger>
// 								<DropdownMenuContent className="w-[280px] border-zinc-700 bg-zinc-800 p-0 text-zinc-300" align="end" forceMount>
// 									<div className="flex gap-5 p-5">
// 										<Avatar className="flex size-9 shrink-0 items-center gap-2 text-neutral-800">
// 											<AvatarImage src={session?.user.image!} alt="User Avatar" />
// 											<AvatarFallback>{session?.user.name}</AvatarFallback>
// 										</Avatar>
// 										<div className="flex min-w-0 flex-1 flex-col items-start">
// 											<p className="truncate text-sm font-semibold">{session?.user.name ?? WEBNAME}</p>
// 											<p className="text-muted-foreground mt-1 truncate text-xs">{session?.user.email ?? "--"}</p>
// 										</div>
// 									</div>
// 									{/* {user?.membershipId === MembershipID.Free && (
// 										<div className="px-[24px] pb-5">
// 											<Button size="sm" className="w-full bg-blue-500 hover:bg-blue-600" onClick={() => setPlanBoxOpen(true)}>
// 												Get a plan
// 											</Button>
// 										</div>
// 									)} */}

// 									<div className="flex flex-row items-center justify-between px-[24px] pb-4">
// 										<div className="flex flex-row items-center gap-1 text-sm font-[350] hover:bg-zinc-800">
// 											<Icon iconNode={coinsStack} className="size-4 text-green-500" />{" "}
// 											{(user?.creditOneTime ?? 0) + (user?.creditFree ?? 0)} credits
// 											{/* <SubmitButton
// 												isSubmitting={refreshingUserCredits}
// 												variant="ghost"
// 												size="icon"
// 												className="h-6 w-6 hover:text-indigo-500"
// 												onClick={() => handleRefreshUserCredits()}
// 											>
// 												<RefreshCw className="size-4" />
// 											</SubmitButton> */}
// 										</div>
// 										<Button size="sm" className="cursor-pointer bg-indigo-500 hover:bg-indigo-600" onClick={() => setPlanBoxOpen(true)}>
// 											Get more
// 										</Button>
// 									</div>
// 									<Separator className="bg-zinc-700" />

// 									<NoPrefetchLink
// 										href="/my-creations"
// 										className="flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100 hover:bg-zinc-700 hover:text-zinc-200"
// 									>
// 										<div className="flex flex-row items-center">
// 											<History className="mr-5 h-4 w-4 shrink-0" />
// 											My Creations
// 										</div>
// 									</NoPrefetchLink>
// 									<NoPrefetchLink
// 										href="/user/my-billing"
// 										className="flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100 hover:bg-zinc-700 hover:text-zinc-200"
// 									>
// 										<div className="flex flex-row items-center">
// 											<CreditCard className="mr-5 h-4 w-4 shrink-0" />
// 											My Billing
// 										</div>
// 										{/* <p className="flex items-center gap-1 rounded bg-muted px-2 py-1 text-xs font-medium">{user?.membershipFormatted}</p> */}
// 									</NoPrefetchLink>
// 									{/* <NoPrefetchLink
// 										href={FEEDBACK_URL}
// 										target="_blank"
// 										className="flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100 hover:bg-muted"
// 									>
// 										<MessageCircle className="mr-5 h-4 w-4 shrink-0" />
// 										Got Feedback
// 									</NoPrefetchLink> */}

// 									<Separator className="bg-zinc-700" />

// 									<button
// 										className="flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100 hover:bg-zinc-700 hover:text-zinc-200"
// 										onClick={handleSignOut}
// 									>
// 										<Power className="mr-5 h-4 w-4 shrink-0" />
// 										Sign out
// 									</button>
// 								</DropdownMenuContent>
// 							</DropdownMenu>
// 						</>
// 					) : (
// 						<Button size="sm" variant="outline" onClick={() => setSignInBoxOpen(true)} className="cursor-pointer text-zinc-800">
// 							Sign In
// 						</Button>
// 					)}
// 				</div>
// 			</div>
// 		</header>
// 	);
// };
