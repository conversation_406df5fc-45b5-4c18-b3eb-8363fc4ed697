import { WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import { allPhotoEffects } from "@/lib/utils-photo-effects";

export const metadata: Metadata = {
	title: `My Creations | ${WEBNAME}`,
	alternates: {
		canonical: "/my-creations",
	},
	robots: {
		index: false,
		follow: false,
	},
};

export default async function Page() {
	return (
		<div className="flex h-full min-h-screen w-full flex-col bg-zinc-900 px-4 py-4">
			<div className="mx-auto flex w-full flex-col pt-4 pb-2">
				<h1 className="text-2xl font-medium whitespace-nowrap text-zinc-200">AI Photo Effects & Filters</h1>
				{/* <p className="text-sm text-zinc-400"></p> */}
			</div>

			<div className="mx-auto grid w-full grid-cols-2 gap-4 pt-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
				{allPhotoEffects.map((item, index) => (
					<div key={index} className="group w-full hover:cursor-pointer">
						<div className="overflow-hidden rounded-md border bg-zinc-800">
							<img
								src={item.previewImageDemo}
								alt=""
								className="aspect-[3/2] rounded-md object-cover transition-transform duration-300 group-hover:scale-105"
								loading="lazy"
							/>
						</div>
						<div className="py-2 text-sm text-zinc-300 group-hover:text-zinc-300/80">{item.name}</div>
					</div>
				))}
			</div>
		</div>
	);
}
