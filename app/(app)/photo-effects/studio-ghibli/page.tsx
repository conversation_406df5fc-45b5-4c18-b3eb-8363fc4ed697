import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import FinalCTA from "@/components/landing/final-cta";
import FAQsComponent from "@/components/landing/faqs";
import { Comparison, ComparisonHandle, ComparisonItem } from "@/components/ui/kibo-ui/comparison";
import Link from "next/link";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { HowToUse } from "@/components/landing/how-to-use";
import FeaturesComparisonComponent from "@/components/landing/features-c";
import PhotoEffectClient from "../_components/photo-effect.client";

export const metadata: Metadata = {
	title: `AI Ghibli Style Generator | ${WEBNAME}`,
	description: "",
	alternates: {
		canonical: "/photo-effects/studio-ghibli",
	},
};

export default async function Page() {
	return (
		<main className="">
			<div className="">
				<PhotoEffectClient />
			</div>

			<div className="relative pt-20 pb-20">
				<div className="mx-auto max-w-5xl px-6">
					<div className="mt-8 space-y-6 text-center sm:mx-auto lg:mt-16 lg:mr-auto">
						<h1 className="mx-auto max-w-4xl text-5xl font-semibold">AI Ghibli Style Generator</h1>
						<div className="text-muted-foreground mx-auto mt-4 md:text-lg"></div>
					</div>

					<div className="mt-8 flex flex-col items-center justify-center gap-2 md:flex-row">
						<Link href="#" className={cn(buttonVariants({ size: "lg" }), `h-12 rounded-full text-base text-nowrap after:content-(--content)`)}>
							Photo to Ghibili Style Now
						</Link>
					</div>

					<div className="mx-auto mt-12 flex w-full max-w-4xl rounded"></div>
				</div>
			</div>
		</main>
	);
}
