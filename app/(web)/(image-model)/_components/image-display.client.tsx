"use client";

import { useState } from "react";
import { LoaderCircle, Download } from "lucide-react";
import { useUserStore } from "@/store/useUserStore";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Hint } from "@/components/ui/custom/hint";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark } from "@/lib/file/utils-file";
import { cn } from "@/lib/utils";

export default function ImageDisplayClient({
	submitting,
	seconds,
	previewImageBase64s,
}: {
	submitting: boolean;
	seconds: string;
	previewImageBase64s: string[] | null;
}) {
	const { hasPaid: userHasPaid } = useUserStore();

	const [downloading, setDownloading] = useState(false);

	return (
		<>
			{previewImageBase64s && (
				<div
					className={cn(
						"grid w-full gap-4",
						previewImageBase64s.length === 1 && "grid-cols-1",
						previewImageBase64s.length === 2 && "grid-cols-2",
						previewImageBase64s.length > 2 && "grid-cols-2 md:grid-cols-4",
					)}
				>
					{previewImageBase64s.map((base64, index) => (
						<div
							key={index}
							className="group relative mx-auto flex aspect-square h-full w-full max-w-md items-center justify-center rounded-lg bg-zinc-800"
						>
							{base64 ? (
								// <img
								// 	src={base64}
								// 	alt={`Generated image ${index + 1}`}
								// 	className="h-full w-full rounded-lg object-contain"
								// 	onContextMenu={(e) => e.preventDefault()}
								// 	onDragStart={(e) => e.preventDefault()}
								// />
								<div
									className="h-full w-full rounded-lg bg-contain bg-center bg-no-repeat"
									style={{ backgroundImage: `url(${base64})` }}
									role="img"
									aria-label={`Generated image ${index + 1}`}
								></div>
							) : (
								submitting && (
									<p className="flex w-full flex-col items-center text-center text-zinc-300">
										<LoaderCircle className="size-6 animate-spin" />
										<span className="text-sm tabular-nums">{seconds}s</span>
									</p>
								)
							)}
							<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
								{base64 && (
									<Hint label="Download image" sideOffset={10}>
										<div className="relative">
											<SubmitButton
												isSubmitting={downloading}
												disabled={!base64}
												size="icon"
												variant="secondary"
												className="cursor-pointer"
												onClick={async () => {
													try {
														setDownloading(true);
														if (userHasPaid) {
															await downloadImageFromBase64(base64);
														} else {
															await downloadImageFromBase64WithWatermark(base64);
														}
													} catch (error) {
														console.error("Failed to download image:", error);
													} finally {
														setDownloading(false);
													}
												}}
											>
												<Download />
											</SubmitButton>
										</div>
									</Hint>
								)}
							</div>
						</div>
					))}
				</div>
			)}
		</>
	);
}
