import { Example } from "@/components/landing/example";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import type { Metadata } from "next";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";
import { Briefcase, CheckCircle, Code, Image, Sparkle, Sparkles, Zap } from "lucide-react";
import { GridSections } from "@/components/landing/grid-sections";
import FAQsComponent from "@/components/landing/faqs";
import FinalCTA from "@/components/landing/final-cta";
import HiDreamI1Client from "../hidream-i1.client";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export const metadata: Metadata = {
	title: `HiDream I1 Free: Try HiDream I1 AI Image Generator | ${WEBNAME}`,
	description:
		"HiDream I1 is an AI image generator with Fast, DEV, and Full versions. Instantly turn text into photorealistic or artistic images—try HiDream I1 for free",
	alternates: {
		canonical: "/hidream/hidream-i1",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/" },
						{ title: "AI Image Generator", href: "/ai-image-generator" },
						{ title: "HiDream", href: "/hidream" },
					]}
					current={"HiDream I1"}
				/>
			</div>

			<section>
				<div className="relative pt-12 pb-16">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold">HiDream I1 AI Image Generator</h1>
							<div className="text-muted-foreground mx-auto mt-4">
								HiDream I1 is an advanced, open-source AI image generator that transforms your text into high-quality, photorealistic or
								artistic images in seconds. Enjoy versatile styles, fast results, and seamless integration for your creative projects. Try
								HiDream I1 for free on Dreampik!
							</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container max-w-4xl">
				<HiDreamI1Client defaultModel="hidream-i1-dev" />
			</div>

			<Example
				title="Get Inspired"
				description="Get inspired by what others are creating with HiDream I1 AI on Dreampik."
				// className="-mt-16"
				images={[
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-i1/example_1.webp`,
						prompt: 'A Ghibli-inspired scene featuring a giant, friendly creature walking through a lush forest and holding a sign saying "HiDream on dreampik.art", with vibrant colors, soft lighting, and whimsical details like floating lights and colorful, magical creatures. The scene captures a peaceful, enchanted atmosphere',
					},
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-i1/example_2.webp`,
						prompt: "An armadillo in a rocket at countdown preparing to blast off to Mars, in the style of ukiyo-e",
					},
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-i1/example_3.webp`,
						prompt: "A majestic waterfall cascading down a cliff in a dense jungle, with mist rising and sunlight filtering through the canopy, creating a rainbow effect, landscape photography",
					},
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-i1/example_4.webp`,
						prompt: "Cinematic still image glowing portrait in a surreal world with shining red grass, minimal soft studio light photography, fine art photo, dark red and white, minimal outfit, redscale color light scheme",
					},
				]}
			/>

			{[
				{
					title: "Text & Image to Image Generation",
					description:
						"Turn your ideas into stunning visuals with HiDream I1. Type a prompt or upload an image, and our AI creates high-quality images that match your vision. Perfect for beginners or pros, it’s easy to generate professional artwork fast. Ideal for marketing, art, or personal projects, HiDream I1 simplifies creativity and delivers impressive results in seconds.",
					image: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-i1/feature-1.webp`,
					imageAlt: "HiDream I1: Text & Image to Image Generation",
					prompt: "Albert Einstein riding a skateboard on the lunar surface, a pirate flag attached, illuminated by the Earth's glow, with craters and fine moon dust kicked up behind him, creating a surreal blend of science, adventure, and whimsy.",
				},
				{
					title: "Photorealistic Generation",
					description:
						"HiDream I1 crafts lifelike images that look like real photos. From detailed portraits to vivid landscapes and product visuals, our AI captures textures, lighting, and shadows with precision. Create professional-grade visuals for business or personal use that grab attention and feel authentic, all generated quickly and effortlessly.",
					image: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-i1/feature-2.webp`,
					imageAlt: "HiDream I1: Photorealistic Generation",
					prompt: "A Nigerian girl wakes up in the middle of the night, lying on her back, eyes wide open, body stiff and frozen, fear etched on her face, in a dimly lit room, shadows dancing on the walls, creating an eerie atmosphere.",
				},
				{
					title: "Diverse Artistic Styles",
					description:
						"Unleash your creativity with HiDream I1’s wide range of artistic styles. From classic oil paintings to modern digital art, our AI tailors images to your unique taste. Perfect for branding or personal projects, explore endless possibilities and create captivating visuals that stand out with just a few clicks.",
					image: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-i1/feature-3.webp`,
					imageAlt: "HiDream I1: Diverse Artistic Styles",
					prompt: "Scratchy pen strokes, blind contour, fisheye perspective close-up, chubby man's face in darkness, stark hatch shaded sketchy scribbly, ink, strong angular shapes, woodcut shading, pen strokes, pouty lips, hungry eyes, minimalist realistic, anime proportions, distorted perspective.",
				},
				{
					title: "Exceptional Detail & Depth",
					description:
						"HiDream I1 delivers images with stunning detail and depth. Our AI masters intricate elements, realistic lighting, and immersive perspectives, making every visual pop. From close-ups to vast scenes, create professional, polished images that captivate and enhance any project with a lifelike, dynamic feel.",
					image: `${OSS_URL_HOST}mkt/pages/image-model/hidream/hidream-i1/feature-4.webp`,
					imageAlt: "HiDream I1:Exceptional Detail & Depth",
					prompt: "Une image saisissante d'une goutte de pluie tombant dans l'océan, avec des microparticules de plastique visibles en suspension, créant un contraste poignant entre la pureté de l'eau et la pollution. Ou un jeune Polynésien debout sur une plage, regardant tristement l'océan pollué, le soleil couchant en arrière-plan.",
				},
			].map((feature, index) => (
				<div key={index} className="container flex flex-col items-center gap-16 px-6 py-20">
					<div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 lg:gap-16">
						<div className={cn("group block w-full", index % 2 === 0 ? "" : "md:order-last")}>
							<AspectRatio ratio={3 / 2} className="group">
								<div className="relative h-full w-full">
									<div className="absolute right-2 bottom-2 left-2 hidden rounded-md border border-zinc-700/50 bg-black/70 px-3 py-2.5 text-xs text-white shadow-lg backdrop-blur-xs group-hover:block lg:block">
										<p className="flex flex-row items-center gap-1.5 pb-1 font-medium text-zinc-100">
											<Sparkle className="h-3.5 w-3.5 text-indigo-400" /> Prompt:
										</p>
										<p className="leading-[1.575] text-zinc-200">{feature.prompt}</p>
									</div>
									<img
										src={feature.image}
										alt={feature.imageAlt ?? feature.title}
										className="h-full w-full rounded-lg object-cover"
										loading="lazy"
									/>
								</div>
							</AspectRatio>
						</div>
						<div className="flex h-full flex-col items-start justify-between gap-4">
							<div className="flex flex-col gap-2">
								<h2 className="text-[32px] font-semibold text-balance">{feature.title}</h2>
								<p className="text-muted-foreground text-base">{feature.description}</p>
							</div>
							<NoPrefetchLink
								href="/hidream/hidream-i1"
								className={cn(buttonVariants({ size: "lg" }), "after:content-(--content)")}
								style={{ "--content": `'Try HiDream I1 Now'` } as React.CSSProperties}
							></NoPrefetchLink>
						</div>
					</div>
				</div>
			))}

			{/* HiDream I1 Model Variants */}
			<section className="bg-zinc-900 py-20">
				<div className="container">
					<h2 className="mb-12 text-center text-3xl font-semibold">HiDream I1 Model Variants</h2>
					<div className="overflow-x-auto rounded-xl border border-zinc-800 bg-zinc-800 shadow-xs">
						<table className="w-full border-collapse">
							<thead>
								<tr className="border-b bg-zinc-700">
									<th className="p-4 text-left font-medium text-zinc-300">Version</th>
									<th className="p-4 text-left font-medium text-zinc-300">Inference Steps</th>
									<th className="p-4 text-left font-medium text-zinc-300">Description</th>
								</tr>
							</thead>
							<tbody>
								<tr className="border-b transition-colors">
									<td className="text-primary p-4 font-semibold">HiDream-I1-Full</td>
									<td className="p-4">
										<span className="rounded-full bg-blue-100 px-2.5 py-1 text-sm font-medium text-blue-800">50</span>
									</td>
									<td className="text-muted-foreground p-4">Complete version with best quality; highest fidelity for detailed images.</td>
								</tr>
								<tr className="border-b transition-colors">
									<td className="text-primary p-4 font-semibold">HiDream-I1-Dev</td>
									<td className="p-4">
										<span className="rounded-full bg-indigo-100 px-2.5 py-1 text-sm font-medium text-indigo-800">28</span>
									</td>
									<td className="text-muted-foreground p-4">Distilled version balancing efficiency and effectiveness; middle ground.</td>
								</tr>
								<tr className="transition-colors">
									<td className="text-primary p-4 font-semibold">HiDream-I1-Fast</td>
									<td className="p-4">
										<span className="rounded-full bg-green-100 px-2.5 py-1 text-sm font-medium text-green-800">16</span>
									</td>
									<td className="text-muted-foreground p-4">Speed-optimized for real-time needs; fewer steps for faster generation.</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</section>

			<GridSections
				title="Why Choose HiDream I1?"
				description="Discover why HiDream I1 is the go-to AI image generator for creators, designers, and businesses. With cutting-edge technology, HiDream I1 transforms your ideas into stunning visuals through text-to-image and image-to-image generation, delivering unmatched quality and flexibility."
				sections={[
					{
						title: "Stunning Image Quality",
						description:
							"Create breathtaking visuals in styles like photorealistic, cartoon, and artistic. HiDream I1 delivers crisp, vibrant images that bring your vision to life.",
						icon: Sparkles,
					},
					{
						title: "Top-Notch Prompt Accuracy",
						description:
							"Get exactly what you describe. HiDream I1 leads in GenEval and DPG benchmarks, outperforming other open-source models for precise text-to-image results.",
						icon: CheckCircle,
					},
					{
						title: "Fully Open Source",
						description:
							"Built for innovation, HiDream I1 is released under the MIT license, giving you the freedom to explore, modify, and integrate it into your projects.",
						icon: Code,
					},
					{
						title: "Commercial Freedom",
						description:
							"Use generated images for personal, research, or commercial projects with no restrictions. HiDream I1 empowers your business with flexible creative tools.",
						icon: Briefcase,
					},
					{
						title: "Lightning-Fast Generation",
						description:
							"Turn ideas into images in seconds. HiDream I1’s optimized 17B-parameter model ensures high-quality results without the wait, even on complex prompts.",
						icon: Zap,
					},
					{
						title: "Versatile Text & Image Input",
						description:
							"Generate or refine visuals with ease. HiDream I1 supports both text-to-image and image-to-image workflows, perfect for any creative project.",
						icon: Image,
					},
				]}
			/>

			<FAQsComponent
				title="HiDream I1 Related FAQs"
				faqs={[
					{
						question: "What is HiDream I1?",
						answer: "HiDream I1 is a powerful AI image generator designed to help you create amazing visuals from text or images in just seconds. With multiple generation modes—fast, dev, and full—HiDream I1 gives you flexibility and control over speed and quality. Whether you need quick drafts or high-quality final images, HiDream I1 is the solution.",
					},
					{
						question: "Which versions of HiDream I1 are available?",
						answer: "We offer Dev and Full versions of HiDream I1. Both versions deliver advanced image generation, with the Full version providing the highest quality results. Choose the option that best fits your creative needs and enjoy better image outputs every time.",
					},
					{
						question: "What projects is HiDream I1 best suited for?",
						answer: "HiDream I1 is perfect for a wide range of creative projects, such as digital art, marketing visuals, concept design, social media content, and more. Its fast and flexible AI makes it easy to bring any idea to life, whether you’re a designer, marketer, or hobbyist.",
					},
					{
						question: "What styles does HiDream I1 AI support?",
						answer: "HiDream I1 supports a variety of styles, from realistic and photorealistic images to cartoons, anime, and digital paintings. You can choose the style that fits your project, ensuring your visuals match your vision every time.",
					},
					{
						question: "How does HiDream I1 compare to other image generation tools?",
						answer: "HiDream I1 stands out with its speed, quality, and versatility. Unlike many other tools, it offers fast generation times, multiple modes for different needs, and high-quality outputs. Whether you want quick drafts or detailed final images, HiDream I1 delivers excellent results.",
					},
					{
						question: "Can I use HiDream I1 for free?",
						answer: "Yes! We provide free credits so you can try HiDream I1 and experience its features without any cost. Upgrade anytime for more credits and advanced options.",
					},
					{
						question: "How fast can HiDream I1 generate images?",
						answer: "HiDream I1 generates images in just a few seconds. With fast mode, you can get results almost instantly—perfect for when you need quick inspiration or rapid prototyping.",
					},
				]}
			/>

			<FinalCTA
				ctaText="Try HiDream I1 Now"
				ctaUrl="/hidream/hidream-i1"
				title="Try HiDream I1 for Free on Dreampik"
				description="Unlock the power of AI with HiDream I1 and turn your ideas into stunning images in seconds. Create, edit, and explore endless possibilities—all in one easy-to-use platform."
			/>
		</main>
	);
}
