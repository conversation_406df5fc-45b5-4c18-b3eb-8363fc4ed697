import { Example } from "@/components/landing/example";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import HiDreamI1Client from "./hidream-i1.client";
import type { Metadata } from "next";
import { buttonVariants } from "@/components/ui/button";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export const metadata: Metadata = {
	title: `HiDream AI Image Generator | ${WEBNAME}`,
	description: "HiDream AI is a text-to-image model .",
	alternates: {
		canonical: "/hidream",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-20">
			<div className="px-6 pt-3">
				<BreadcrumbPath
					paths={[
						{ title: "Home", href: "/" },
						{ title: "AI Image Generator", href: "/ai-image-generator" },
					]}
					current={"HiDream"}
				/>
			</div>

			<section>
				<div className="relative pt-12 pb-16">
					<div className="mx-auto max-w-5xl px-6">
						<div className="text-center sm:mx-auto lg:mt-0 lg:mr-auto">
							<h1 className="mx-auto mt-8 max-w-4xl text-5xl font-semibold lg:mt-16">HiDream AI Image Generator</h1>
							<div className="text-muted-foreground mx-auto mt-4 text-lg">Create HiDream AI images from Dreampik AI for free✨✨</div>
						</div>
					</div>
				</div>
			</section>

			<div className="container max-w-4xl pb-20">
				<HiDreamI1Client defaultModel="hidream-i1-dev" />
			</div>

			{/* Explore */}
			<div className="bg-zinc-900 py-20">
				<div className="container">
					<h2 className="mb-8 text-center text-3xl font-semibold">Explore HiDream AI{"'"}s Models</h2>
					<div className="flex flex-row justify-center gap-4">
						<NoPrefetchLink href="/hidream/hidream-i1" className={buttonVariants()}>
							HiDream I1
						</NoPrefetchLink>
						<NoPrefetchLink href="/hidream/hidream-e1" className={buttonVariants()}>
							HiDream E1
						</NoPrefetchLink>
					</div>
				</div>
			</div>

			<Example
				title="Get Inspired"
				description="Get inspired by what others are creating with Dreampik"
				// className="-mt-16"
				images={[
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						prompt: 'A Ghibli-inspired scene featuring a giant, friendly creature walking through a lush forest and holding a sign saying "HiDream on dreampik.art", with vibrant colors, soft lighting, and whimsical details like floating lights and colorful, magical creatures. The scene captures a peaceful, enchanted atmosphere',
					},
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						prompt: "An armadillo in a rocket at countdown preparing to blast off to Mars, in the style of ukiyo-e",
					},
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						prompt: "A majestic waterfall cascading down a cliff in a dense jungle, with mist rising and sunlight filtering through the canopy, creating a rainbow effect, landscape photography",
					},
					{
						model: "HiDream I1 Dev",
						url: `${OSS_URL_HOST}mkt/pages/home/<USER>
						prompt: "Cinematic still image glowing portrait in a surreal world with shining red grass, minimal soft studio light photography, fine art photo, dark red and white, minimal outfit, redscale color light scheme",
					},
				]}
			/>

			{/* <FeaturesComponent
				ctaText="Convert Photo to Ghibli Style"
				ctaUrl="/studio-ghibli"
				features={[
					{
						title: ",
						description:
							"",
						image: `${OSS_URL_HOST}mkt/.webp`,
						imageAlt: `${WEBNAME}:`,
					}, 
				]}
			/> */}

			{/* <GridSections
				title=""
				description="""
				sections={[
					{
						title: "",
						description:
							"",
						icon: ScanEye,
					},
				]}
			/> */}

			{/* <FAQsComponent
				faqs={[
					{
						question: "",
						answer: "",
					},
				]}
			/> */}

			{/* <FinalCTA
				ctaText=""
				ctaUrl="/"
				title=""
				description=""
			/> */}
		</main>
	);
}
