"use client";

import { useState } from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import {
	LucideIcon,
	SparklesIcon,
	Square,
	RectangleHorizontal,
	RectangleVertical,
	LoaderCircle,
	Download,
	Icon,
	LayoutGrid,
	Scan,
	Palette,
	Ban,
	ImagePlus,
	Loader2,
	X,
} from "lucide-react";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, Credits402<PERSON>rror, handleError, IgnoreError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Hint } from "@/components/ui/custom/hint";
import { CLICK_GEN_IMAGE_START } from "@/lib/umami-event-name";
import { coinsStack } from "@lucide/lab";
import { downloadImageFromBase64, downloadImageFromBase64WithWatermark, fileToBase64, imageUrlToBase64 } from "@/lib/file/utils-file";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { ideogramCredits } from "@/lib/utils-image-model";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { uploadFile } from "@/lib/file/upload-file";
import { Dropzone } from "@/components/ui/kibo-ui/dropzone";

const IMAGE_SIZES = [
	{ label: "1:1", width: 1024, height: 1024, icon: Square },
	{ label: "2:3", width: 683, height: 1024, icon: RectangleVertical },
	{ label: "3:2", width: 1024, height: 683, icon: RectangleHorizontal },
	{ label: "3:4", width: 768, height: 1024, icon: RectangleVertical },
	{ label: "4:3", width: 1024, height: 768, icon: RectangleHorizontal },
	{ label: "9:16", width: 576, height: 1024, icon: RectangleVertical },
	{ label: "16:9", width: 1024, height: 576, icon: RectangleHorizontal },
] as const;
const RENDER_SPEEDS = [
	{ label: "Turbo", value: "TURBO" },
	{ label: "Default", value: "BALANCED" },
	{ label: "Quality", value: "QUALITY" },
] as const;
const STYLES = [
	{ label: "General", value: "GENERAL", icon: LayoutGrid },
	{ label: "Realistic", value: "REALISTIC", icon: Scan },
	{ label: "Design", value: "DESIGN", icon: Palette },
] as const;

export default function IdeogramClient() {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [prompt, setPrompt] = useState("");
	const [size, setSize] = useState<{
		width: number;
		height: number;
		icon: LucideIcon;
	}>(IMAGE_SIZES[0]);
	const [renderSpeed, setRenderSpeed] = useState<{
		label: string;
		value: string;
	}>(RENDER_SPEEDS[1]);
	const numImages = 1;
	const [style, setStyle] = useState<{
		label: string;
		value: string;
		icon: LucideIcon;
	} | null>(null);
	const [originImage, setOriginImage] = useState<{
		url: string;
		base64: string;
	} | null>(null);
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const [previewImageBase64s, setPreviewImageBase64s] = useState<string[] | null>(null);
	// const [previewImageBase64s, setPreviewImageBase64s] = useState<string[] | null>([
	// 	"https://static.youstylize.com/dev/image_result/202504/2025040701960f88c96272eb9f49f2e5e5ea7c0f.png",
	// ]);

	const handleLocalFileDrop = async (files: File[]) => {
		if (!session?.user) {
			setSignInBoxOpen(true);
			return;
		}

		setOriginImage(null);

		if (!files || files.length === 0) return;

		try {
			setUploadingImage(true);

			const { file_url } = await uploadFile(files[0]);
			const base64 = await fileToBase64(files[0]);
			setOriginImage({
				url: file_url,
				base64,
			});
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		try {
			setPreviewImageBase64s(Array(numImages).fill(null));
			setSubmitting(true);
			const { status, message, resultUrls } = await ofetch("/api/v1/image/ideogram", {
				method: "POST",
				body: {
					prompt: promtpTrim,
					size: {
						width: size.width,
						height: size.height,
					},
					renderSpeed: renderSpeed.value,
					numImages: numImages,
					style: style?.value,
					image: originImage?.url,
				},
			});
			handleError(status, message);
			refreshUser();
			// const resultUrls = [
			// 	"https://d32s1zkpjdc4b1.cloudfront.net/output/18220959-c7e1-4f70-9a6c-3479a6fe4881-u2_b13edcbc-9723-46c0-95cb-99cadec85930.jpeg",
			// 	"https://v3.fal.media/files/rabbit/voFEj_podAvkaiEcyiHoB_image.png",
			// ];
			if (resultUrls && resultUrls.length > 0) {
				// Create a copy of the current array to modify
				const tempBase64s = Array(resultUrls.length).fill(null);
				// Process each URL and update the state as each completes
				for (let i = 0; i < resultUrls.length; i++) {
					const base64 = await imageUrlToBase64(resultUrls[i] as string);
					tempBase64s[i] = base64;
					setPreviewImageBase64s([...tempBase64s]);
				}
			}

			toast.success("Generate success.");
		} catch (error: any) {
			console.error("Failed to generate image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}

			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error("Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState(false);

	return (
		<div className="flex w-full flex-col items-center gap-6">
			<div className="w-full rounded-lg border bg-zinc-800 px-4 pt-4 pb-2">
				<Textarea
					placeholder="Describe an image and click generate..."
					value={prompt}
					maxLength={1000}
					onChange={(e) => setPrompt(e.target.value)}
					className="h-[126px] resize-none shadow-none focus-visible:ring-0 [&::-webkit-scrollbar]:my-2 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600"
				/>

				<div className="mt-3 mb-1 flex flex-wrap items-center justify-between gap-1">
					<div className="flex items-center gap-1">
						{originImage ? (
							<div className="group relative aspect-square h-8 w-12 rounded-full border">
								<img
									src={originImage.base64}
									alt="Model"
									className="h-full w-full rounded-full object-cover"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
								/>
								<button
									className="absolute -top-1.5 -right-1.5 z-10 rounded-full"
									onClick={(e) => {
										e.stopPropagation();
										setOriginImage(null);
									}}
								>
									<X className="size-[20px] cursor-pointer rounded-full border bg-zinc-800 p-1 text-white shadow" strokeWidth={3} />
								</button>
							</div>
						) : (
							<Dropzone
								multiple={false}
								maxFiles={1}
								onDrop={(files) => handleLocalFileDrop(files)}
								accept={{
									"image/jpeg": [".jpg", ".jpeg", ".png", ".webp"],
								}}
								className="h-8 w-12 cursor-pointer rounded-full border py-0 dark:bg-zinc-900/50 dark:hover:bg-zinc-800"
							>
								{uploadingImage ? <Loader2 className="animate-spin text-zinc-300" /> : <ImagePlus />}
							</Dropzone>
						)}
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									size="sm"
									className="cursor-pointer justify-between rounded-full border bg-zinc-900/50 hover:bg-zinc-800"
								>
									<div className="flex items-center gap-2">
										<size.icon className="" />
										{IMAGE_SIZES.find((s) => s.width === size.width && s.height === size.height)?.label}
									</div>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="">
								<p className="text-muted-foreground px-2 py-1.5 text-sm">Aspect ratio</p>
								{IMAGE_SIZES.map((sizeOption, index) => (
									<DropdownMenuItem
										key={index}
										className="cursor-pointer"
										onClick={() => setSize({ width: sizeOption.width, height: sizeOption.height, icon: sizeOption.icon })}
									>
										<div className="flex items-center gap-2">
											<sizeOption.icon className="size-4" />
											{sizeOption.label}
										</div>
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									size="sm"
									className="cursor-pointer justify-between rounded-full border bg-zinc-900/50 hover:bg-zinc-800"
								>
									<div className="flex items-center gap-2">{renderSpeed.label}</div>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="">
								{/* <p className="px-2 py-1.5 text-sm text-muted-foreground">Number of images</p>
								<Tabs value={numImages.toString()} className="px-2">
									<TabsList className="h-12 w-full gap-0.5">
										{[1, 2, 3, 4].map((num, index) => (
											<TabsTrigger
												key={index}
												value={num.toString()}
												onClick={() => setNumImages(num)}
												className="h-10 w-full font-normal hover:bg-zinc-200"
											>
												{num}
											</TabsTrigger>
										))}
									</TabsList>
								</Tabs>

								<Separator className="my-3" /> */}

								<p className="text-muted-foreground px-2 py-1.5 text-sm">Render speed</p>
								<Tabs value={renderSpeed.value} className="px-2">
									<TabsList className="h-12 gap-0.5">
										{RENDER_SPEEDS.map((speed, index) => (
											<TabsTrigger
												key={index}
												value={speed.value}
												onClick={() => setRenderSpeed(speed)}
												className="h-10 cursor-pointer font-normal"
											>
												{speed.label}
											</TabsTrigger>
										))}
									</TabsList>
								</Tabs>
								<div className="flex flex-row items-start justify-between gap-2 px-2 pt-1 pb-2 text-[10px]">
									<p className="text-muted-foreground flex flex-col items-start leading-tight">
										<span>Faster</span>
										<span>More affordable</span>
									</p>
									<p className="text-muted-foreground flex flex-col items-end leading-tight">
										<span>Slower</span>
										<span>More detail</span>
									</p>
								</div>
							</DropdownMenuContent>
						</DropdownMenu>
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									size="sm"
									className="cursor-pointer justify-between rounded-full border bg-zinc-900/50 hover:bg-zinc-800"
								>
									<div className="flex items-center gap-2">{style ? STYLES.find((s) => s.value === style.value)?.label : "No style"}</div>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="">
								<p className="text-muted-foreground px-2 py-1.5 text-sm">Style</p>

								<DropdownMenuItem className="cursor-pointer" onClick={() => setStyle(null)}>
									<div className="flex items-center gap-2">
										<Ban className="size-4" />
										No style
									</div>
								</DropdownMenuItem>
								{STYLES.map((styleOption, index) => (
									<DropdownMenuItem key={index} className="cursor-pointer" onClick={() => setStyle(styleOption)}>
										<div className="flex items-center gap-2">
											<styleOption.icon className="size-4" />
											{styleOption.label}
										</div>
									</DropdownMenuItem>
								))}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>

					<SubmitButton
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						disabled={submitting || prompt.trim().length === 0}
						data-umami-event={CLICK_GEN_IMAGE_START}
						data-umami-event-model={`ideogram-3-${renderSpeed.value.toLowerCase()}`}
						className="cursor-pointer bg-indigo-500 hover:bg-indigo-500/80"
						variant="secondary"
					>
						<SparklesIcon className="h-4 w-4" />
						<span className="hidden md:block">Generate</span>{" "}
						{session && (
							<p className="flex flex-row items-center gap-0.5 text-xs font-normal [&_svg]:size-3">
								(<span>{ideogramCredits(numImages, renderSpeed.value)}</span> <Icon iconNode={coinsStack} />)
							</p>
						)}
					</SubmitButton>
				</div>
			</div>

			{previewImageBase64s && (
				<div
					className={cn(
						"grid w-full gap-4",
						previewImageBase64s.length === 1 && "grid-cols-1",
						previewImageBase64s.length === 2 && "grid-cols-2",
						previewImageBase64s.length > 2 && "grid-cols-2 md:grid-cols-4",
					)}
				>
					{previewImageBase64s.map((base64, index) => (
						<div
							key={index}
							className="group relative mx-auto flex aspect-square h-full w-full max-w-md items-center justify-center rounded-lg bg-zinc-800"
						>
							{base64 ? (
								// <img
								// 	src={base64}
								// 	alt={`Generated image ${index + 1}`}
								// 	className="h-full w-full rounded-lg object-contain"
								// 	onContextMenu={(e) => e.preventDefault()}
								// 	onDragStart={(e) => e.preventDefault()}
								// />
								<div
									className="h-full w-full rounded-lg bg-contain bg-center bg-no-repeat"
									style={{ backgroundImage: `url(${base64})` }}
									role="img"
									aria-label={`Generated image ${index + 1}`}
								></div>
							) : (
								submitting && (
									<p className="flex w-full flex-col items-center text-center text-lg text-zinc-300">
										<LoaderCircle className="size-6 animate-spin" />
										<span className="text-sm tabular-nums">{seconds}s</span>
									</p>
								)
							)}
							<div className="absolute right-2 bottom-2 z-10 items-center gap-1">
								{base64 && (
									<Hint label="Download image" sideOffset={10}>
										<div className="relative">
											<SubmitButton
												isSubmitting={downloading}
												disabled={!base64}
												size="icon"
												variant="secondary"
												className="cursor-pointer"
												onClick={async () => {
													try {
														setDownloading(true);
														if (userHasPaid) {
															await downloadImageFromBase64(base64);
														} else {
															await downloadImageFromBase64WithWatermark(base64);
														}
													} catch (error) {
														console.error("Failed to download image:", error);
													} finally {
														setDownloading(false);
													}
												}}
											>
												<Download />
											</SubmitButton>
										</div>
									</Hint>
								)}
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	);
}
