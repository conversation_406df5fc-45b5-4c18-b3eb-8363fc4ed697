"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { SparklesIcon, Download, Icon, Trash, Loader2, ImagePlus, ChevronDown, CoinsIcon } from "lucide-react";
import { useSession } from "@/lib/auth-client";
import { useUserStore } from "@/store/useUserStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { toast } from "sonner";
import { ofetch } from "ofetch";
import { AuthError, Credits402Error, handleError, IgnoreError } from "@/@types/error";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { Hint } from "@/components/ui/custom/hint";
import { CLICK_GEN_VIDEO_START } from "@/lib/umami-event-name";
import { downloadImageFromUrl } from "@/lib/file/utils-file";
import { calculateProgress, cn, sleep } from "@/lib/utils";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { WEBNAME } from "@/lib/constants";
import { getVideoModelCredits, HAILUO_2_STANDARD, pageVideoModels, VideoModel } from "@/lib/utils-video-model";
import { MediaResultStatus } from "@/@types/media/media-type";
import { Progress } from "@/components/ui/custom/progress";
import Link from "next/link";
import { Dropzone, DropzoneEmptyState } from "@/components/ui/kibo-ui/dropzone/index";
import { uploadFile } from "@/lib/file/upload-file";

const genTypes = [
	{
		label: "Text to Video",
		value: "text-to-video",
	},
	{
		label: "Image to Video",
		value: "image-to-video",
	},
];

export default function PageVideoClient({
	defaultModelId = HAILUO_2_STANDARD.id,
	defaultGenType = "text-to-video",
}: {
	defaultModelId?: string;
	defaultGenType?: string;
}) {
	const { data: session } = useSession();
	const { user, refreshUser, hasPaid: userHasPaid } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();

	const [genVideoType, setGenVideoType] = useState(genTypes[0]);
	const [model, setModel] = useState<VideoModel>(pageVideoModels.find((m) => m.id === defaultModelId) ?? HAILUO_2_STANDARD);
	const [prompt, setPrompt] = useState("");
	const [paramsInfo, setParamsInfo] = useState<{
		model: VideoModel;
	}>({
		model: pageVideoModels.find((m) => m.id === defaultModelId) ?? HAILUO_2_STANDARD,
	});

	const [image, setImage] = useState<string | null>(null);
	const [uploadingImage, setUploadingImage] = useState<boolean>(false);
	const handleLocalFileDrop = async (files: File[]) => {
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		if (!files || files.length === 0) return;

		try {
			setUploadingImage(true);
			const { file_url } = await uploadFile(files[0]);
			// const file_url = "";
			setImage(file_url);
		} catch (error: any) {
			console.error("Failed to upload image:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Upload image failed: ${error.message}`);
		} finally {
			setUploadingImage(false);
		}
	};

	const [previewVideo, setPreviewVideo] = useState<string | null>(null);
	// const [previewVideo, setPreviewVideo] = useState<string | null>(
	// 	"https://videocdn.pollo.ai/web-cdn/pollo/production/cmatg0lee0dulm5azhx6vajaz/video/1749178015507-038828b0-3441-4715-8ec2-4fa894252929.mp4",
	// );
	const [submitting, setSubmitting] = useState(false);
	const { seconds } = useSubmitTimer(submitting);
	const handleGenerateImage = async () => {
		if (submitting) return;
		if (!session) {
			setSignInBoxOpen(true);
			return;
		}
		const promtpTrim = prompt.trim();
		if (!promtpTrim) return;

		setParamsInfo({
			model: model,
		});

		try {
			setPreviewVideo(null);
			setSubmitting(true);
			const { status, message, request_id } = await ofetch("/api/v1/video/page-video", {
				method: "POST",
				body: {
					model: model.id,
					prompt: promtpTrim,
					image: image,
				},
			});
			handleError(status, message);
			refreshUser();

			let taskStatus = MediaResultStatus.InProgress;
			let taskError = null;

			await sleep((paramsInfo.model.time ?? 60) * 1000);

			// get task status
			while (taskStatus !== MediaResultStatus.Completed && taskStatus !== MediaResultStatus.Failed) {
				await new Promise((resolve) => setTimeout(resolve, 7500)); // wait for 7.5 seconds
				let {
					status: request_status,
					message: request_message,
					taskStatus: reuqest_taskStatus,
					taskError: request_taskError,
					resultUrls,
				} = await ofetch("/api/v1/video/status", {
					method: "POST",
					body: { id: request_id },
				});
				handleError(request_status, request_message);
				taskStatus = reuqest_taskStatus;
				taskError = request_taskError;
				if (resultUrls[0]) {
					setPreviewVideo(resultUrls[0]);
				}
			}
			if (taskStatus === MediaResultStatus.Failed) {
				if (taskError) {
					throw new Error(taskError);
				}
				throw new Error("Generate video failed. Please try again or contact support.");
			}

			// setPreviewVideo(
			// 	"https://videocdn.pollo.ai/web-cdn/pollo/production/cmatg0lee0dulm5azhx6vajaz/video/1749178015507-038828b0-3441-4715-8ec2-4fa894252929.mp4",
			// );
		} catch (error: any) {
			console.error("Failed to generate video:", error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}

			if (error instanceof IgnoreError) {
				return;
			}
			if (error instanceof Credits402Error) {
				toast.warning("You do not have enough credits.", {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			toast.error("Generate failed.");
		} finally {
			setSubmitting(false);
		}
	};

	const [downloading, setDownloading] = useState(false);

	return (
		<div className="flex w-full flex-col items-center gap-6">
			<div className="w-full max-w-2xl rounded-xl border bg-zinc-800 shadow">
				<div className="flex flex-row items-center justify-center gap-3 border-b border-zinc-700 px-4 pt-3 pb-2 text-sm">
					{genTypes.map((type, index) => (
						<button
							key={index}
							className={cn(
								"cursor-pointer font-medium",
								type === genVideoType ? "text-indigo-500 underline decoration-2 underline-offset-12" : "text-zinc-400 hover:text-indigo-500",
							)}
							onClick={() => {
								if (type.value === defaultGenType) {
									setModel(pageVideoModels.find((m) => m.id === defaultModelId)!);
								} else {
									if (type.value === "image-to-video" && !model.imageToVideo) {
										setModel(pageVideoModels.find((m) => m.imageToVideo) ?? HAILUO_2_STANDARD);
									}
									if (type.value === "text-to-video" && !model.textToVideo) {
										setModel(pageVideoModels.find((m) => m.textToVideo) ?? HAILUO_2_STANDARD);
									}
								}
								setGenVideoType(type);
							}}
						>
							{type.label}
						</button>
					))}
				</div>

				<div className="space-y-3 px-4 py-3">
					{genVideoType.value === "image-to-video" && (
						<div className="space-y-[6px]">
							<p className="text-sm text-zinc-300">Image</p>
							{image ? (
								<div className="group relative h-[144px] w-full rounded-lg border bg-zinc-100 hover:bg-zinc-200">
									<img
										src={image}
										alt="Model"
										className="h-[142px] w-full rounded-lg object-contain"
										onContextMenu={(e) => e.preventDefault()}
										onDragStart={(e) => e.preventDefault()}
									/>
									<div className="absolute inset-0 hidden items-center justify-center bg-black/50 group-hover:flex">
										<button
											onClick={() => setImage(null)}
											className="cursor-pointer rounded-full p-2 text-zinc-300/80 transition-colors hover:text-white"
										>
											<Trash className="size-4" />
										</button>
									</div>
								</div>
							) : (
								<div
									onClick={() => {
										if (!session) {
											setSignInBoxOpen(true);
										}
									}}
								>
									<Dropzone
										multiple={false}
										maxFiles={1}
										noClick={!session}
										onDragEnter={() => {
											if (!session) {
												setSignInBoxOpen(true);
												return;
											}
										}}
										onDrop={(files) => handleLocalFileDrop(files)}
										accept={{
											"image/jpeg": [".jpg", ".jpeg", ".png", ".webp"],
										}}
										onError={console.error}
										className="h-[144px] w-full cursor-pointer p-0 hover:bg-zinc-100"
									>
										<DropzoneEmptyState>
											<>
												{uploadingImage ? (
													<Loader2 className="animate-spin text-zinc-400" />
												) : (
													<div className="text-muted-foreground flex h-10 flex-col items-center gap-2 font-normal hover:text-zinc-500">
														<ImagePlus />
														<p>Click to upload an image</p>
													</div>
												)}
												{/* <p className="w-full text-sm font-normal text-muted-foreground">Or drop an image</p> */}
											</>
										</DropzoneEmptyState>
									</Dropzone>
								</div>
							)}
						</div>
					)}

					<div className="space-y-[6px]">
						<p className="text-xs text-zinc-300">Prompt</p>
						<Textarea
							placeholder="Describe your video scene..."
							rows={5}
							maxLength={2000}
							className={cn(
								"h-32 resize-none shadow-none focus-visible:shadow-sm focus-visible:ring-0",
								"[&::-webkit-scrollbar]:my-1 [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-zinc-600",
							)}
							value={prompt}
							onChange={(e) => setPrompt(e.target.value)}
						/>
					</div>
				</div>

				<div className="mb-2 flex flex-wrap items-center justify-between gap-1 px-4">
					<div className="flex items-center gap-1">
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									size="sm"
									className="cursor-pointer justify-between border bg-zinc-900/50 font-normal hover:bg-zinc-800"
								>
									<img src={model.logo} className="size-4" alt="" />
									{model?.name}
									<ChevronDown className="text-muted-foreground" />
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent className="w-[180px]">
								{pageVideoModels.map((modelOption, index) => {
									if (genVideoType.value === "image-to-video" && !modelOption.imageToVideo) {
										return null;
									}
									if (genVideoType.value === "text-to-video" && !modelOption.textToVideo) {
										return null;
									}
									return (
										<DropdownMenuItem key={index} className="cursor-pointer items-center" onClick={() => setModel(modelOption)}>
											<img src={modelOption.logo} className="size-4" alt="" />
											{modelOption.name}
										</DropdownMenuItem>
									);
								})}
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
					<SubmitButton
						variant="secondary"
						className="cursor-pointer bg-indigo-500 hover:bg-indigo-500/80"
						isSubmitting={submitting}
						onClick={handleGenerateImage}
						disabled={submitting || prompt.trim().length === 0 || (genVideoType.value === "image-to-video" && !image)}
						data-umami-event={CLICK_GEN_VIDEO_START}
						data-umami-event-model={model.id}
					>
						<SparklesIcon className="h-4 w-4" />
						<span className="hidden md:block">Generate</span>{" "}
						{session && (
							<p className="flex flex-row items-center gap-0.5 text-xs font-normal">
								(<span>{getVideoModelCredits(model, model.durationAll![0], model.resolutionAll ? model.resolutionAll[0] : null)}</span>
								<CoinsIcon className="size-3" />)
							</p>
						)}
					</SubmitButton>
				</div>
			</div>

			<div className="w-full">
				{submitting ? (
					<div className="mx-auto flex aspect-video w-full max-w-2xl shrink-0 rounded-xl border bg-zinc-800">
						<div className="mx-auto flex flex-col items-center justify-center gap-2 px-4 text-center text-zinc-300">
							<span className="text-sm tabular-nums">{seconds}s</span>
							<div className="flex flex-row items-center gap-1 text-xs">
								<Progress
									value={calculateProgress(Number(seconds), paramsInfo.model.time ?? 60)}
									// value={50}
									className="h-1.5 w-[280px] bg-zinc-500"
									indicatorClassName="bg-indigo-600"
								/>
								<span className="font-mono tabular-nums">{calculateProgress(Number(seconds), paramsInfo.model.time ?? 60)}%</span>
							</div>
							<div className="text-xs font-[350]">
								{WEBNAME} is generating your video, which may take {paramsInfo.model.time} seconds. Once finished, the video will be saved in{" "}
								<Link href="/my-creations" target="_blank" className="text-indigo-500 hover:underline hover:underline-offset-4">
									My Creations
								</Link>
								.
							</div>
						</div>
					</div>
				) : (
					previewVideo && (
						<div className={cn("group relative mx-auto w-full max-w-2xl rounded-xl")}>
							<video
								src={previewVideo}
								muted
								controls
								controlsList="nodownload noplaybackrate"
								disableRemotePlayback
								className="h-full w-full rounded-xl object-contain"
								onContextMenu={(e) => e.preventDefault()}
								onDragStart={(e) => e.preventDefault()}
								onMouseEnter={(e) => (e.target as HTMLVideoElement).play()}
							/>
							<div className="absolute top-2 right-2 z-10 items-center gap-1">
								{previewVideo && (
									<Hint label="Download" sideOffset={10}>
										<div className="relative">
											<SubmitButton
												className="cursor-pointer"
												isSubmitting={downloading}
												disabled={!previewVideo}
												size="icon"
												variant="secondary"
												onClick={async () => {
													try {
														setDownloading(true);
														if (userHasPaid) {
															await downloadImageFromUrl(previewVideo);
														} else {
															setPlanBoxOpen(true);
														}
													} catch (error) {
														console.error("Failed to download:", error);
													} finally {
														setDownloading(false);
													}
												}}
											>
												<Download />
											</SubmitButton>
										</div>
									</Hint>
								)}
							</div>
						</div>
					)
				)}
			</div>
		</div>
	);
}
