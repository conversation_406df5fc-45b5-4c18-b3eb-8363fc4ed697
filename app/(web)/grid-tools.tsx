import { Fragment } from "react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { AppModelType, AppToolsType } from "@/config/app-tools";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

export function GridTools({ title, tools }: AppToolsType) {
	return (
		<div className="container flex flex-col gap-8 px-6">
			<h2 className="text-lg font-medium text-pretty">{title}</h2>
			<div className="grid w-full grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
				{tools.map((tool, index) => (
					<Link key={index} href={tool.url} className={cn("group flex flex-row items-center gap-2")}>
						<div className={cn("flex h-[42px] w-[42px] items-center justify-center rounded-lg border bg-zinc-700")}>
							<tool.icon className={cn("h-4 w-4 transition-transform duration-300")} />
						</div>
						<div className="flex flex-col">
							<p className="text-base font-medium">{tool.title}</p>
							{tool.description && <p className="text-muted-foreground text-sm font-[350]">{tool.description}</p>}
						</div>
					</Link>
				))}
			</div>
		</div>
	);
}

export function GridModels({ models, description }: { description?: string; models: AppModelType[] }) {
	return (
		<div className="container flex flex-col gap-8 px-6">
			<div className="0 rounded-lg border bg-zinc-700 p-4">
				{description && <p className="mb-2 text-sm text-zinc-300">{description}</p>}
				<div className="flex w-full flex-row flex-wrap items-center gap-2">
					{models.map((model, index) => (
						<Fragment key={index}>
							<div className="flex flex-row gap-0.5">
								<Link
									href={model.url}
									className={cn(
										"group flex flex-row items-center gap-2 text-sm hover:text-indigo-500 hover:underline hover:underline-offset-4",
									)}
								>
									<p className="">{model.name}</p>
								</Link>
								{model.new && <Badge className="rounded-full bg-green-500 px-1.5 py-0.5 text-[10px]">New</Badge>}
							</div>
							{index < models.length - 1 && <Separator orientation="vertical" className="h-3 w-px bg-zinc-500" />}
						</Fragment>
					))}
				</div>
			</div>
		</div>
	);
}
