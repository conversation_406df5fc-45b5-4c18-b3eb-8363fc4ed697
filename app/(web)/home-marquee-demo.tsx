"use client";

import React, { useState } from "react";
import SimpleMarquee from "@/fancy/components/blocks/simple-marquee";
import { OSS_URL_HOST } from "@/lib/constants";

const exampleImages = [
	`${OSS_URL_HOST}mkt/pages/home/<USER>
	`${OSS_URL_HOST}mkt/pages/home/<USER>
	`${OSS_URL_HOST}mkt/pages/home/<USER>
	`${OSS_URL_HOST}mkt/pages/home/<USER>
	`${OSS_URL_HOST}mkt/pages/home/<USER>
	`${OSS_URL_HOST}mkt/pages/home/<USER>
	`${OSS_URL_HOST}mkt/pages/home/<USER>
	`${OSS_URL_HOST}mkt/pages/home/<USER>
	`${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_1.webp`,
	`${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_2.webp`,
	`${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_3.webp`,
	`${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_4.webp`,
	`${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_5.webp`,
	`${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_6.webp`,
	`${OSS_URL_HOST}mkt/pages/image-model/imagen/imagen4/example_8.webp`,
];

const MarqueeItem = ({ children }: { children: React.ReactNode }) => (
	<div className="mx-2 cursor-pointer duration-300 ease-in-out hover:scale-105 sm:mx-3 md:mx-4">{children}</div>
);

export function HomeMarqueeDemo() {
	const firstThird = exampleImages.slice(0, Math.floor(exampleImages.length / 3));
	const secondThird = exampleImages.slice(Math.floor(exampleImages.length / 3), Math.floor((2 * exampleImages.length) / 3));
	const lastThird = exampleImages.slice(Math.floor((2 * exampleImages.length) / 3));

	const [container, setContainer] = useState<HTMLElement | null>(null);

	return (
		<div
			className="relative flex h-[256px] flex-col items-center justify-center overflow-auto overflow-x-hidden sm:h-[312px] md:h-[416px]"
			ref={(node) => setContainer(node)}
		>
			<div className="absolute top-0 flex w-full flex-col items-center justify-center space-y-2 sm:space-y-3 md:space-y-4">
				<SimpleMarquee
					className="w-full"
					baseVelocity={4}
					repeat={4}
					draggable={false}
					scrollSpringConfig={{ damping: 50, stiffness: 400 }}
					slowDownFactor={0.1}
					slowdownOnHover
					slowDownSpringConfig={{ damping: 60, stiffness: 300 }}
					scrollAwareDirection={true}
					scrollContainer={{ current: container! }}
					useScrollVelocity={true}
					direction="left"
				>
					{firstThird.map((src, i) => (
						<MarqueeItem key={i}>
							<img src={src} alt={`Image ${i + 1}`} className="h-20 w-32 object-cover sm:h-24 sm:w-40 md:h-32 md:w-48" />
						</MarqueeItem>
					))}
				</SimpleMarquee>

				<SimpleMarquee
					className="w-full"
					baseVelocity={3}
					repeat={4}
					scrollAwareDirection={true}
					scrollSpringConfig={{ damping: 50, stiffness: 400 }}
					slowdownOnHover
					slowDownFactor={0.1}
					slowDownSpringConfig={{ damping: 60, stiffness: 300 }}
					useScrollVelocity={true}
					scrollContainer={{ current: container! }}
					draggable={false}
					direction="right"
				>
					{secondThird.map((src, i) => (
						<MarqueeItem key={i}>
							<img src={src} alt={`Image ${i + firstThird.length}`} className="h-20 w-32 object-cover sm:h-24 sm:w-40 md:h-32 md:w-48" />
						</MarqueeItem>
					))}
				</SimpleMarquee>

				<SimpleMarquee
					className="w-full"
					baseVelocity={4}
					repeat={4}
					draggable={false}
					scrollSpringConfig={{ damping: 50, stiffness: 400 }}
					slowDownFactor={0.1}
					slowdownOnHover
					slowDownSpringConfig={{ damping: 60, stiffness: 300 }}
					scrollAwareDirection={true}
					scrollContainer={{ current: container! }}
					useScrollVelocity={true}
					direction="left"
				>
					{lastThird.map((src, i) => (
						<MarqueeItem key={i}>
							<img
								src={src}
								alt={`Image ${i + firstThird.length + secondThird.length}`}
								className="h-20 w-32 object-cover sm:h-24 sm:w-40 md:h-32 md:w-48"
							/>
						</MarqueeItem>
					))}
				</SimpleMarquee>
			</div>
		</div>
	);
}
