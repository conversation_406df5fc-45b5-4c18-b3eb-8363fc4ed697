// import { serverSideTranslation } from '@/lib/i18n'
import { i18nConfig } from "@/i18n-config";
import { Header } from "@/components/navigate/header";
import Footer from "@/components/navigate/footer";
import FooterBadge from "@/components/navigate/footer-badge";
import { AnalyticsClarity } from "@/components/analytics/analytics-clarity";
import { AnalyticsGoolge } from "@/components/analytics/analytics-google";

type Params = Promise<{ lang: string }>;
export default async function RootLayout({ children, params }: { children: React.ReactNode; params: Params }) {
	// console.log("lang lang: ", lang)
	// const { t } = await serverSideTranslation(lang, 'common')
	return (
		<div className="flex min-h-screen flex-col">
			<Header />
			{children}
			<Footer />
			<FooterBadge />
			<AnalyticsClarity />
			<AnalyticsGoolge />
		</div>
	);
}
