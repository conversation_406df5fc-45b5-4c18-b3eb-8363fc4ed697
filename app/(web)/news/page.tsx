import { Fragment } from "react";
import type { Metadata } from "next";
import { WEBNAME } from "@/lib/constants";
import { format } from "date-fns";
import { mentionsAll, newsAll } from "@/config/news";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { ChevronDownIcon } from "lucide-react";

export const metadata: Metadata = {
	title: `${WEBNAME} in the news`,
	description: `Read the latest news about ${WEBNAME}.`,
	alternates: {
		canonical: "/news",
	},
};

export default async function Page() {
	return (
		<div className="flex min-h-screen w-full grow flex-col">
			<div className="bg-zinc-50 pt-12 pb-20">
				<div className="container max-w-3xl">
					<div className="mx-auto space-y-3">
						<h1 className="mt-8 mb-4 text-3xl font-bold tracking-tight md:text-5xl">In the news</h1>
						<p className="text-muted-foreground text-base">Your AI Image & Video Creation Platform</p>
					</div>
				</div>
			</div>

			<div className="container mx-auto mt-16 mb-8 max-w-3xl space-y-4 md:mt-24">
				<h3 className="pb-2">Latest news</h3>
				{newsAll.map((news, index) => (
					<Fragment key={index}>
						<Link href={news.url} target="_blank" rel="noopener noreferrer nofollow" className="group flex w-full flex-col gap-2">
							<p className="text-foreground/90 font-medium group-hover:underline group-hover:underline-offset-4">{news.title}</p>
							<span className="text-muted-foreground text-sm">{format(news.publishedAt, "MMM d, yyyy")}</span>
						</Link>
						{index < newsAll.length - 1 && <Separator className="" />}
					</Fragment>
				))}
			</div>

			<div className="container mx-auto mt-8 mb-32 max-w-3xl space-y-4">
				<div className="divide-y divide-zinc-200/80">
					<details className="group bg-muted rounded-md border px-4 py-1" open={false}>
						<summary className="flex cursor-pointer list-none items-center justify-start gap-3 py-2 text-[13px] leading-6 font-normal text-zinc-700 hover:no-underline">
							<ChevronDownIcon className="text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200 group-open:rotate-180" />
							{WEBNAME} has also been mentioned
						</summary>
						<div className="flex w-full flex-row flex-wrap items-center gap-1.5 ps-7 pb-2">
							{mentionsAll.map((mention, index) => (
								<Fragment key={index}>
									<Link
										href={mention.url}
										target="_blank"
										rel="noopener noreferrer nofollow"
										className="group text-[13px] text-zinc-800 hover:underline hover:underline-offset-4"
									>
										<p className="">{mention.name}</p>
									</Link>
									{index < mentionsAll.length - 1 && <Separator orientation="vertical" className="h-3 w-px bg-zinc-500" />}
								</Fragment>
							))}
						</div>
					</details>
				</div>
			</div>
		</div>
	);
}
