import { Button, buttonVariants } from "@/components/ui/button";
import { Card, CardHeader } from "@/components/ui/card";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { WEBNAME } from "@/lib/constants";
import { cn } from "@/lib/utils";
import type { Metadata } from "next";

export const metadata: Metadata = {
	title: `Confirmation | ${WEBNAME}`,
	alternates: {
		canonical: "/user",
	},
	robots: {
		index: false,
		follow: false,
	},
};
export default function Page() {
	return (
		<div className="flex min-h-screen flex-col items-center justify-center p-6">
			<Card className="-mt-14 w-full max-w-md p-0 shadow-xl transition-all duration-300 hover:shadow-indigo-800">
				<CardHeader className="p-8">
					<div className="flex flex-col items-center">
						<div className="text-center">
							<p className="text-lg">Thank you! Your checkout is now being processed.</p>
							<p className="text-muted-foreground mt-2 text-sm">Please wait a moment, it may take a few seconds or minutes.</p>
						</div>
					</div>

					<div className="pt-4">
						<NoPrefetchLink
							href="/user/my-billing"
							className={cn(
								buttonVariants({ variant: "default" }),
								"w-full bg-linear-to-r from-indigo-500 to-indigo-600 py-2 font-medium text-white transition-all duration-300 hover:from-indigo-500 hover:to-indigo-600 hover:shadow-md",
							)}
						>
							Go to my billing
						</NoPrefetchLink>
					</div>

					<div className="flex justify-center">
						<div className="text-muted-foreground mt-2 animate-bounce text-xs">✨ Your journey with us begins now ✨</div>
					</div>
				</CardHeader>
			</Card>
		</div>
	);
}
