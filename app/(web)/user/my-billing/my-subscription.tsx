"use client";

import { useState } from "react";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import { toast } from "sonner";
import { UserInfoDB } from "@/@types/user";
import { Card, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MembershipID, membershipMapping } from "@/@types/membership-type";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useRouter } from "nextjs-toploader/app";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Subscription } from "@/@types/subscription";
import { AuthError, handleError } from "@/@types/error";
import { useUserPlanBoxOpenStore } from "@/store/useUserPlanBoxOpenStore";
import { cn } from "@/lib/utils";
import { ofetch } from "ofetch";
import { round } from "lodash";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { getMembershipProductInfo, getSubscriptionStatusName, SubscriptionStatus } from "@/lib/utils-membership";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { EllipsisVertical, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { EMAIL_CONTACT } from "@/lib/constants";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export default function MySubscription({ user, subscriptions }: { user: UserInfoDB; subscriptions: Subscription[] }) {
	const router = useRouter();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { userPlanBoxOpen, setUserPlanBoxOpen, setUserSubscription } = useUserPlanBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const [isResuming, setIsResuming] = useState(false);
	const [handleSubscription, setHandleSubscription] = useState<{
		type: "resume" | "cancel" | "portal" | string;
		subscriptionId?: string;
	} | null>(null);
	const [isGettingCustomerPortalUrl, setIsGettingCustomerPortalUrl] = useState(false);

	// Suspend usage due to API error
	const resumeSubscription = async (subscriptionId: string) => {
		if (isResuming) return;
		try {
			setIsResuming(true);
			const { status, message } = await ofetch("/api/payment/subscription", {
				method: "POST",
				body: { subscriptionId, type: "resume" },
			});
			handleError(status, message);
			router.refresh();
			toast.success("Subscription resumed.");
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error("Subscription resume failed.");
		} finally {
			setIsResuming(false);
		}
	};

	const getCustomerPortalUrl = async (type: string, subscriptionId?: string) => {
		if (isGettingCustomerPortalUrl) return;
		try {
			setHandleSubscription({ type, subscriptionId });
			setIsGettingCustomerPortalUrl(true);
			const { status, message, url } = await ofetch("/api/payment/portal", {
				method: "POST",
				body: {},
			});
			handleError(status, message);
			window.open(url, "_blank");
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error("Failed to open customer portal.");
		} finally {
			setHandleSubscription(null);
			setIsGettingCustomerPortalUrl(false);
		}
	};

	return (
		<div className="flex h-full w-full grow flex-col overflow-hidden">
			<ScrollArea className="h-full w-full px-6 md:px-8">
				<div className="mx-auto flex w-full max-w-3xl pt-20 pb-2">
					<h1 className="text-2xl font-medium whitespace-nowrap">My subscription</h1>
				</div>

				<div className="mx-auto flex max-w-3xl flex-col gap-6 pt-4">
					<div className="flex flex-col gap-4">
						<p className="leading-none font-medium tracking-tight text-neutral-700">Plan</p>
						<div className="flex flex-col gap-3">
							{user.membershipId === MembershipID.Free && (
								<Card className="w-full rounded-md bg-neutral-50 shadow-none">
									{subscriptions.length > 0 && (
										<div className="relative">
											<Badge
												variant="default"
												className="absolute right-0 -translate-y-1/2 rounded-full bg-green-600 font-normal hover:bg-green-600"
											>
												Current plan
											</Badge>
										</div>
									)}
									<CardHeader className="px-4 py-2.5">
										<div className="flex flex-row items-center justify-between gap-2">
											<div className="flex flex-row items-center gap-1">
												<p className="font-medium">Free</p>
											</div>

											<Button variant="ghost" onClick={() => setPlanBoxOpen(true)} className="text-indigo-500 hover:text-indigo-600">
												Upgrade plan
											</Button>
										</div>
									</CardHeader>
								</Card>
							)}
							{user.membershipId !== MembershipID.Free && subscriptions.length === 0 && (
								<Card className="w-full rounded-md bg-red-100 shadow-none">
									<CardHeader className="flex h-[56px] flex-row items-center justify-between gap-2 px-4 py-2.5">
										<div className="flex flex-row items-center gap-1">
											<p className="leading-none font-medium">{membershipMapping[user.membershipId as MembershipID].name}</p>
										</div>
									</CardHeader>
								</Card>
							)}
							{subscriptions.map((subscription, index) => (
								<Card key={index} className="w-full rounded-md bg-neutral-50 shadow-none">
									{subscriptions.length > 1 && subscription.subscriptionId === user.subscriptionId && (
										<div className="relative">
											<Badge
												variant="default"
												className="absolute right-0 -translate-y-1/2 rounded-full bg-green-900 font-normal hover:bg-green-900"
											>
												Current plan
											</Badge>
										</div>
									)}
									<CardHeader className="space-y-0 px-4 py-2.5">
										<div className="flex flex-row items-center justify-between gap-2">
											<div className="flex flex-row items-center gap-1">
												<p className="leading-none font-medium">
													{getMembershipProductInfo(subscription.productId)?.membership.name}
													<span className="text-muted-foreground ml-1 text-sm">
														({getMembershipProductInfo(subscription.productId)?.period.name})
													</span>
												</p>
												<Badge
													variant="secondary"
													className={cn(
														subscription.status === SubscriptionStatus.Active
															? "bg-green-100 text-green-600 hover:bg-green-100 hover:text-green-600"
															: "bg-yellow-50 text-yellow-600 hover:bg-yellow-50 hover:text-yellow-600",
														"rounded-full font-normal",
													)}
												>
													{getSubscriptionStatusName(subscription.status as SubscriptionStatus)}
												</Badge>
											</div>
											{/* <Button
												variant="ghost"
												onClick={() => {
													setUserPlanBoxOpen(true);
													setUserSubscription(subscription);
												}}
												className="text-indigo-500 hover:text-indigo-600"
											>
												Change plan
											</Button> */}
											<Popover>
												<PopoverTrigger className={buttonVariants({ variant: "outline", size: "icon" })}>
													<EllipsisVertical className="h-4 w-4" />
												</PopoverTrigger>
												<PopoverContent className="-w-[8rem] p-1">
													<div
														onClick={() => {
															setUserPlanBoxOpen(true);
															setUserSubscription(subscription);
														}}
														className="hover:bg-accent cursor-pointer rounded-sm px-2 py-1.5 text-sm font-medium text-indigo-500 transition-colors [&>svg]:size-4 [&>svg]:shrink-0"
													>
														Change plan
													</div>
													{!subscription.cancelAtPeriodEnd && (
														<div
															onClick={() => {
																if (isGettingCustomerPortalUrl) return;
																getCustomerPortalUrl("cancel", subscription.subscriptionId);
															}}
															className={cn(
																"hover:bg-accent flex cursor-pointer flex-row items-center gap-1 rounded-sm px-2 py-1.5 text-sm font-normal transition-colors [&>svg]:size-4 [&>svg]:shrink-0",
																isGettingCustomerPortalUrl &&
																	handleSubscription?.type === "cancel" &&
																	handleSubscription.subscriptionId === subscription.subscriptionId &&
																	"cursor-not-allowed opacity-50",
															)}
														>
															Cancel ↗
															{isGettingCustomerPortalUrl &&
																handleSubscription?.type === "cancel" &&
																handleSubscription.subscriptionId === subscription.subscriptionId && (
																	<Loader2 className="h-4 w-4 animate-spin" />
																)}
														</div>
													)}
												</PopoverContent>
											</Popover>
										</div>
										<div className="text-muted-foreground flex flex-row items-center gap-1 text-xs">
											{subscription.cancelAtPeriodEnd ? (
												<>
													{subscription.currentPeriodEndAt && (
														<p className="">Ends on {format(subscription.currentPeriodEndAt, "MMM d, yyyy")}</p>
													)}
													<span> · </span>
													<button
														className="border-foreground flex flex-row items-center border-b text-sm text-indigo-500"
														onClick={() => {
															getCustomerPortalUrl("resume", subscription.subscriptionId);
															// resumeSubscription(subscription.subscriptionId);
														}}
													>
														Resume
														{handleSubscription?.type === "resume" &&
															handleSubscription.subscriptionId === subscription.subscriptionId && (
																<Loader2 className="h-4 w-4 animate-spin" />
															)}
													</button>
												</>
											) : (
												<>
													{subscription.currentPeriodEndAt && (
														<p className="">Next renews on {format(subscription.currentPeriodEndAt, "MMM d, yyyy")}</p>
													)}
												</>
											)}
										</div>
									</CardHeader>
								</Card>
							))}

							{((user.membershipId === MembershipID.Free && subscriptions.length > 0) || subscriptions.length > 1) && (
								<div className="rounded-md border border-yellow-200 bg-yellow-50 p-4">
									<p className="text-sm text-yellow-700">
										We{"'"}ve detected an issue with your subscription information. Please{" "}
										<NoPrefetchLink href={`mailto:${EMAIL_CONTACT}`} className="font-medium text-yellow-800 underline" target="_blank">
											contact our support team
										</NoPrefetchLink>{" "}
										for assistance in resolving this matter.
									</p>
								</div>
							)}
						</div>
					</div>

					<div className="flex flex-col gap-4">
						<p className="leading-none font-medium tracking-tight text-neutral-700">Credits</p>

						<Card className="w-full rounded-md bg-neutral-50 shadow-none">
							<CardHeader className="px-4 py-0">
								<div className="flex h-[56px] flex-row items-center justify-between gap-2">
									<div className="flex flex-row items-center gap-1">
										<div className="flex flex-row items-center gap-1">
											<p className="leading-none font-medium">
												{user.membershipId === MembershipID.Free && round(user.creditFree, 1)}
												{user.membershipId === MembershipID.Starter && round(user.creditSubscription, 1)}
												{user.membershipId === MembershipID.Growth && "Unlimited"}
											</p>
										</div>
									</div>
								</div>
							</CardHeader>
						</Card>
					</div>

					{subscriptions.length > 0 && (
						<div className="flex flex-col gap-4">
							<p className="leading-none font-medium tracking-tight text-neutral-700">Billing information</p>

							<Card className="w-full rounded-md bg-neutral-50 shadow-none">
								<CardHeader className="px-4 py-2.5">
									<div className="flex flex-row items-center justify-between gap-2">
										<div className="flex flex-row items-center gap-1">
											<p className="text-sm text-neutral-700">{user.email}</p>
										</div>

										<SubmitButton
											isSubmitting={isGettingCustomerPortalUrl && handleSubscription?.type === "portal"}
											variant="ghost"
											onClick={() => getCustomerPortalUrl("portal")}
											className="text-indigo-500 hover:text-indigo-600"
										>
											Billing history
										</SubmitButton>
									</div>
								</CardHeader>
							</Card>
						</div>
					)}

					<Dialog open={userPlanBoxOpen} onOpenChange={setUserPlanBoxOpen}>
						<DialogTitle className="h-0" />
						<DialogContent className="max-w-max rounded-lg p-0">
							<ScrollArea className="max-h-[90vh] overflow-y-auto" type="always">
								<div className="flex flex-col items-center gap-2 p-6 md:p-8">{/*<Plans isUserPlan={true} />*/}</div>
							</ScrollArea>
						</DialogContent>
					</Dialog>
				</div>
			</ScrollArea>
		</div>
	);
}
