import { WEBNAME } from "@/lib/constants";
import { UserInfoDB } from "@/@types/user";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { refreshUser } from "@/server/refresh-user";
import { eq } from "drizzle-orm";
import { getDB } from "@/server/db/db-client.server";
import SignIn from "@/components/shared/sigin-in";
import type { Metadata } from "next";
import MyBilling from "./my-billing.client";
import { orderSchema, User, userSchema } from "@/server/db/schema.server";

type Params = Promise<{ lang: string }>;

export const metadata: Metadata = {
	title: `My Billing | ${WEBNAME}`,
	alternates: {
		canonical: "/user/my-billing",
	},
	robots: {
		index: false,
		follow: false,
	},
};

// async function getMySubscriptions() {
// 	const sessionUser = await getCurrentSessionUser();
// 	if (!sessionUser) return { userInfo: null, subscriptions: [] };
// 	const userInfo: UserInfoDB | null = await refreshUser(sessionUser.id);
// 	if (!userInfo) return { userInfo: null, subscriptions: [] };

// 	const subscriptions: Subscription[] = await db
// 		.select({
// 			id: subscriptionSchema.id,
// 			userId: subscriptionSchema.userId,

// 			subscriptionId: subscriptionSchema.subscriptionId,
// 			status: subscriptionSchema.status,
// 			recurringInterval: subscriptionSchema.recurringInterval,

// 			productId: subscriptionSchema.productId,

// 			currentPeriodStartAt: subscriptionSchema.currentPeriodStartAt,
// 			currentPeriodEndAt: subscriptionSchema.currentPeriodEndAt,
// 			cancelAtPeriodEnd: subscriptionSchema.cancelAtPeriodEnd,
// 			canceledAt: subscriptionSchema.canceledAt,
// 			startAt: subscriptionSchema.startAt,
// 			endAt: subscriptionSchema.endAt,
// 			endedAt: subscriptionSchema.endedAt,
// 		})
// 		.from(subscriptionSchema)
// 		// .where(and(eq(subscriptionSchema.userId, userInfo.id), or(eq(subscriptionSchema.status, "cancelled"), eq(subscriptionSchema.status, "active"))))
// 		.where(and(eq(subscriptionSchema.userId, userInfo.id), eq(subscriptionSchema.status, "active")))
// 		.orderBy(desc(subscriptionSchema.id));

// 	return { userInfo, subscriptions };
// }

async function getUser() {
	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) return { userInfo: null, hasOrder: false };

	const db = getDB();
	const { user, order } = await db.transaction(async (tx) => {
		const [user]: User[] = await tx.select().from(userSchema).where(eq(userSchema.id, sessionUser.id));
		const [order] = await tx.select({ id: orderSchema.id }).from(orderSchema).where(eq(orderSchema.userId, sessionUser.id)).limit(1);
		return { user, order };
	});
	if (!user) {
		return { userInfo: null, hasOrder: false };
	}

	const userInfo: UserInfoDB | null = await refreshUser(sessionUser.id, {
		existUser: user,
	});
	if (!userInfo) return { userInfo: null, hasOrder: false };

	return { userInfo, hasOrder: false };
	// return { userInfo, hasOrder: dbData[1].length > 0 };
}

export default async function Page({ params }: { params: Params }) {
	// const { lang } = await params;

	const { userInfo: userInfo, hasOrder } = await getUser();

	if (!userInfo) {
		return (
			<div className="flex h-full min-h-screen items-center justify-center">
				<div className="-mt-14 w-full max-w-md rounded-xl border bg-zinc-900 p-6 shadow-sm">
					<SignIn />
				</div>
			</div>
		);
	}

	return <MyBilling user={userInfo} hasOrder={hasOrder} />;
}
