"use client";

import { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CalendarIcon, ChevronLeft, Dot, Send } from "lucide-react";
import { toast } from "sonner";
import { SubmitButton } from "@/components/ui/custom/submit-button";
import { ofetch } from "ofetch";
import { handleError, ParamsError } from "@/@types/error";
import { useBlogCategoryStore } from "@/store/admin/useBlogCategoryStore";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import useAutosizeTextArea from "@/hooks/useAutosizeTextArea";
import urlSlug from "url-slug";
import Editor from "@/components/novel/advanced-editor";
import { Separator } from "@/components/ui/separator";
import { useRouter } from "nextjs-toploader/app";
import { BlogSchemaType, getBlogStatusText } from "@/@types/admin/blog/blog";
import { JSONContent } from "novel";
import Document from "@tiptap/extension-document";
import Paragraph from "@tiptap/extension-paragraph";
import Text from "@tiptap/extension-text";
import { Bold } from "@tiptap/extension-bold";
import BulletList from "@tiptap/extension-bullet-list";
import Heading from "@tiptap/extension-heading";
import { Link as TipTapLink } from "@tiptap/extension-link";
import ListItem from "@tiptap/extension-list-item";
import TextStyle from "@tiptap/extension-text-style";
import { Color } from "@tiptap/extension-color";
import TaskItem from "@tiptap/extension-task-item";
import TaskList from "@tiptap/extension-task-list";
import OrderedList from "@tiptap/extension-ordered-list";
import Image from "@tiptap/extension-image";
import CodeBlock from "@tiptap/extension-code-block";
import Blockquote from "@tiptap/extension-blockquote";
import { generateJSON } from "@tiptap/html";
import { WEBHOST } from "@/lib/constants";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export default function EditBlogComponent({ blogId, blog }: { blogId: number | null | undefined; blog: BlogSchemaType | null | undefined }) {
	const router = useRouter();

	const { categories } = useBlogCategoryStore();

	const [isSubmitting, setIsSubmitting] = useState(false);

	const [title, setTitle] = useState<string>("");
	const titleTextAreaRef = useRef<HTMLTextAreaElement>(null);
	useAutosizeTextArea(titleTextAreaRef.current, title);

	const [languageCode, setLanguageCode] = useState<string>("en");
	const [slug, setSlug] = useState<string>("");
	const [metaTitle, setMetaTitle] = useState<string>("");
	const [metaDescription, setMetaDescription] = useState<string>("");

	const [intro, setIntro] = useState<string>("");
	const [categoryId, setCategoryId] = useState<string>("");
	// const [featuredImageUrl, setFeaturedImageUrl] = useState<string>("");
	const [publishedAt, setPublishedAt] = useState<Date>();
	const [blogStatus, setBlogStatus] = useState<string>("0");
	const [blogStatusServer, setBlogStatusServer] = useState<string>("");

	const [html, setHtml] = useState<string>();
	// const [initialHtmlJson, setInitialHtmlJson] = useState<JSONContent>();
	const [initialHtmlJson, setInitialHtmlJson] = useState<JSONContent | undefined>(
		blog?.html
			? generateJSON(blog.html, [
					Document,
					Bold,
					Heading,
					TipTapLink,
					Paragraph,
					BulletList,
					OrderedList,
					ListItem,
					TaskList,
					TaskItem,
					Text,
					TextStyle,
					Color,
					Image,
					CodeBlock,
					Blockquote,
				])
			: undefined,
	);

	useEffect(() => {
		if (blog) {
			setTitle(blog.title);
			setLanguageCode(blog.lang);
			setSlug(blog.slug);
			setCategoryId(blog.categoryId ? blog.categoryId.toString() : "");
			setMetaTitle(blog.metaTitle || "");
			setMetaDescription(blog.metaDescription || "");
			// setImage(blog.image);
			setIntro(blog.intro);
			setHtml(blog.html);
			setPublishedAt(blog.publishedAt);
			setBlogStatus(blog.status.toString());
			setBlogStatusServer(blog.status.toString());

			// if (blog.html) {
			// 	setInitialHtmlJson(generateJSON(blog.html, [Bold, BulletList, Document, Heading, TipTapLink, Paragraph, ListItem, Text, TextStyle, Color]));
			// }
		}
	}, []);

	const onSubmit = async () => {
		if (isSubmitting) return;

		const titleTrim = title.trim();
		const slugTrim = slug.trim();
		const introTrim = intro.trim();

		if (!titleTrim || !languageCode || !slugTrim || !introTrim) {
			toast.info("Please fill in all fields");
			return;
		}

		try {
			setIsSubmitting(true);

			let requestBody: any = {
				slug: slugTrim,
				title: titleTrim,
				categoryId: categoryId ? Number(categoryId) : undefined,
				lang: languageCode,
				metaTitle: metaTitle,
				metaDescription: metaDescription,
				// image: featuredImageUrl,
				intro: introTrim,
				html: html,
				publishedAt: publishedAt ?? new Date(),
				status: Number(blogStatus),
			};
			if (blogId) requestBody.id = blogId;

			const { status, message, newBlogId } = await ofetch("/api/admin/blog/blog-item", {
				method: "POST",
				body: requestBody,
			});
			handleError(status, message);
			if (blogId) {
				setBlogStatusServer(blogStatus);
				toast.success("Blog updated successfully");
				setIsSubmitting(false);
			} else {
				toast.success("Blog added successfully");
				if (newBlogId) {
					router.push(`/admin/blog/${newBlogId}`);
				}
			}
		} catch (error) {
			if (error instanceof ParamsError) {
				toast.error(error.message);
			} else {
				toast.error("An error occurred");
			}
			setIsSubmitting(false);
		}
	};

	return (
		<div className="flex h-full w-full flex-1 flex-row">
			<div className="flex h-full w-full flex-col overflow-hidden border-r">
				<div className="flex w-full flex-row items-center justify-between p-4">
					<div className="flex flex-row items-center">
						<NoPrefetchLink href={`/admin/blog?_=${new Date().getTime()}`} className={cn("", "flex flex-row items-center text-sm font-normal")}>
							<ChevronLeft className="mr-1 h-4 w-4" />
							Blogs
						</NoPrefetchLink>
						<p className={cn("flex flex-row items-center text-xs", blogStatusServer === "1" ? "text-green-500" : "text-muted-foreground")}>
							<Dot />
							<span>{getBlogStatusText(Number(blogStatusServer))}</span>
						</p>
					</div>
					<SubmitButton isSubmitting={isSubmitting} size="sm" onClick={onSubmit}>
						<p className="flex flex-row items-center gap-1">
							<Send />
							<span>Save</span>
						</p>
					</SubmitButton>
				</div>

				<div className="mx-auto w-full max-w-3xl px-4">
					<div className="flex w-full flex-col space-y-4">
						<div className="">
							<Textarea
								ref={titleTextAreaRef}
								value={title}
								rows={1}
								className="min-h-[40px] resize-none rounded-none border-none p-0 text-2xl font-semibold shadow-none focus-visible:ring-0 focus-visible:outline-hidden"
								onChange={(e) => {
									setTitle(e.target.value);
									if (blogId) return;
									if (e.target.value) {
										setSlug(urlSlug(e.target.value));
									} else {
										setSlug("");
									}
								}}
								placeholder="Title"
							/>
						</div>
						{intro && <p className="text-primary">{intro}</p>}
						<Separator className="" />
					</div>
				</div>

				<div className="h-full grow overflow-hidden">
					<ScrollArea className="h-full w-full" type="always">
						<div className="mx-auto max-w-3xl px-4 py-4">
							{/* <Editor initialHtml={html} onChange={setHtml} /> */}
							<Editor initialHtmlJson={initialHtmlJson} onChange={setHtml} />
						</div>
					</ScrollArea>
				</div>
			</div>

			<div className="h-full w-[360px] py-4 lg:w-[420px]">
				<Label className="flex-1 px-4 text-base">Blog settings</Label>
				<div className="h-full grow overflow-hidden pb-2">
					<ScrollArea className="h-full w-full" type="always">
						<div className="my-4 space-y-6 px-4">
							<div className="flex flex-col items-start gap-2">
								<Label>Language *</Label>
								<Select value={languageCode} onValueChange={(value) => setLanguageCode(value as string)}>
									<SelectTrigger id="framework">
										<SelectValue placeholder="Select Language">English</SelectValue>
									</SelectTrigger>
									<SelectContent position="popper">
										<SelectItem value="en" className="cursor-pointer">
											English
										</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div className="flex flex-col items-start gap-2">
								<Label className="">Blog slug *</Label>
								<Input value={slug} onChange={(e) => setSlug(e.target.value)} placeholder="Slug" className="shrink-0" disabled={!!blogId} />
								<NoPrefetchLink href={`${WEBHOST}/blog/${slug}`} target="_blank" className="text-xs text-gray-500 hover:underline">
									{WEBHOST}/blog/{slug}
								</NoPrefetchLink>
							</div>
							<Accordion type="single" collapsible className="">
								<AccordionItem value="item-1" className="border-0">
									<AccordionTrigger className="py-0 text-sm hover:no-underline">Meta</AccordionTrigger>
									<AccordionContent>
										<div className="mt-2 w-full space-y-3 rounded-md border border-gray-300 p-2">
											<div className="flex flex-col items-start gap-2">
												<Label className="text-xs">Title</Label>
												<Textarea value={metaTitle} rows={2} onChange={(e) => setMetaTitle(e.target.value)} placeholder="Meta title" />
											</div>
											<div className="flex flex-col items-start gap-2">
												<Label className="text-xs">Description</Label>
												<Textarea
													value={metaDescription}
													rows={2}
													onChange={(e) => setMetaDescription(e.target.value)}
													placeholder="Meta description"
												/>
											</div>
										</div>
									</AccordionContent>
								</AccordionItem>
							</Accordion>
							<div className="flex flex-col items-start gap-2">
								<Label>Summary(Intro) *</Label>
								<Textarea value={intro} rows={4} onChange={(e) => setIntro(e.target.value)} placeholder="Summary(Intro)" />
							</div>
							<div className="flex flex-col items-start gap-2">
								<Label>Category</Label>
								<Select value={categoryId} onValueChange={(value) => setCategoryId(value as string)}>
									<SelectTrigger id="framework">
										<SelectValue placeholder="Select Category">
											{categories.find((category) => category.id === Number(categoryId))?.name}
										</SelectValue>
									</SelectTrigger>
									<SelectContent position="popper">
										{categories.map((category, index) => (
											<SelectItem key={index} value={String(category.id)} className="cursor-pointer">
												{category.name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>
							{/* <div className="flex flex-col items-start gap-2">
								<Label className="">Featured Image</Label>
								<Input value={title || ""} onChange={(e) => setTitle(e.target.value)} placeholder="Image URL" className="shrink-0" />
							</div> */}
							<div className="flex flex-col items-start gap-2">
								<Label className="">Date published</Label>
								<div className="flex w-full items-center justify-between gap-1">
									<Popover modal={true}>
										<PopoverTrigger asChild>
											<Button variant={"outline"} className={cn("w-full font-normal", !publishedAt && "text-muted-foreground")}>
												<CalendarIcon />
												{publishedAt ? format(publishedAt, "P") : <span>Pick a date</span>}
											</Button>
										</PopoverTrigger>
										<PopoverContent className="w-auto p-0" align="start">
											<Calendar
												mode="single"
												selected={publishedAt ? publishedAt : undefined}
												onSelect={(date) => {
													setPublishedAt(date);
												}}
												disabled={(date) => date < new Date("1900-01-01")}
											/>
										</PopoverContent>
									</Popover>
									{/* {publishedAt ? (
										<Button variant="ghost" size="icon" className="shrink-0" onClick={() => setPublishedAt(undefined)}>
											<CircleX />
										</Button>
									) : (
										<div className="h-9 w-9 shrink-0" />
									)} */}
								</div>
							</div>
							<div className="flex flex-col items-start gap-2">
								<Label>Status</Label>
								<Select value={blogStatus} onValueChange={(value) => setBlogStatus(value as string)}>
									<SelectTrigger>
										<SelectValue placeholder="Select Category">{getBlogStatusText(Number(blogStatus))}</SelectValue>
									</SelectTrigger>
									<SelectContent position="popper">
										<SelectItem value="0" className="cursor-pointer">
											Draft
										</SelectItem>
										<SelectItem value="1" className="cursor-pointer">
											Published
										</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>
					</ScrollArea>
				</div>
			</div>
		</div>
	);
}
