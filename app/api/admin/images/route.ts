import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { eq, count, desc, and } from "drizzle-orm";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { isNil } from "lodash";
import { MediaHeadType } from "@/@types/media/media-type";

export async function GET(req: Request) {
	if (!(await checkAuthAdmin())) {
		return NextResponse.json({ status: 401, message: "Unauthorized" });
	}

	const url = new URL(req.url);
	const needTotal = url.searchParams.get("needTotal");
	const paramVisibility = url.searchParams.get("visibility");
	const visibility: boolean | null = isNil(paramVisibility) ? null : paramVisibility === "true";
	const page = parseInt(url.searchParams.get("page") || "1", 10);
	const pageSize = parseInt(url.searchParams.get("pageSize") || "20", 10);
	const startIndex = (page - 1) * pageSize;
	const limit = pageSize;

	const db = getDB();
	const images: any[] = await db
		.select({
			id: mediaItemSchema.id,
			uid: mediaItemSchema.uid,
			userId: mediaItemSchema.userId,
			mediaHeadUid: mediaItemSchema.mediaHeadUid,
			visibility: mediaItemSchema.visibility,
			mediaPaths: mediaItemSchema.mediaPaths,
			mediaOriginUrls: mediaItemSchema.mediaOriginUrls,
			createdAt: mediaItemSchema.createdAt,
			prompt: mediaHeadSchema.prompt,
			model: mediaHeadSchema.model,
			status: mediaHeadSchema.status,
		})
		.from(mediaItemSchema)
		.leftJoin(mediaHeadSchema, eq(mediaItemSchema.mediaHeadUid, mediaHeadSchema.uid))
		.where(and(eq(mediaItemSchema.type, MediaHeadType.Image), visibility === null ? undefined : eq(mediaItemSchema.visibility, visibility)))
		.orderBy(desc(mediaItemSchema.id))
		.limit(limit)
		.offset(startIndex);

	if (!needTotal) {
		return NextResponse.json({ status: 200, message: "success", images });
	}

	const totalCountData = await db
		.select({ count: count(mediaItemSchema.id) })
		.from(mediaItemSchema)
		.where(and(eq(mediaItemSchema.type, MediaHeadType.Image), visibility === null ? undefined : eq(mediaItemSchema.visibility, visibility)));
	const total = totalCountData[0].count;
	const totalPage = Math.ceil(total / pageSize);

	return NextResponse.json({ status: 200, message: "success", images, total, totalPage });
}
