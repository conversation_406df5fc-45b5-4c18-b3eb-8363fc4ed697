import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { WEBHOST, WEBNAME } from "@/lib/constants";
// import { MembershipID } from "@/@types/membership-type";
// import { getUser } from "@/server/utils-user.server";
// import { Polar } from "@polar-sh/sdk";
import { createCheckout, lemonSqueezySetup } from "@lemonsqueezy/lemonsqueezy.js";

interface Params {
	productId?: string;
	type: "subscription" | "onetime";
}
export async function POST(req: Request) {
	const params: Params = await req.json();
	if (!params.productId) {
		return NextResponse.json({ status: 400, message: "No variant ID was provided." });
	}
	const cfIpCountryCode = req.headers.get("cf-ipcountry");

	// 获取用户信息
	const sessionUser = await getCurrentSessionUser();
	const user = sessionUser;
	// const user = sessionUser ? await getUser(sessionUser.id) : null;
	if (!user) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	// if (params.type === "subscription") {
	// 	if (user.membershipId !== MembershipID.Free) {
	// 		return NextResponse.json({ status: 1001, message: "You are already a member." });
	// 	}
	// }

	try {
		// Lemon Squeezy
		lemonSqueezySetup({ apiKey: process.env.LEMONSQUEEZY_API_KEY });
		const checkout = await createCheckout(process.env.LEMONSQUEEZY_STORE_ID!, params.productId, {
			checkoutData: {
				email: user.email, // Displays in the checkout form
				custom: {
					user_id: user.id, // Sent in the background; visible in webhooks and API calls
				},
			},
			productOptions: {
				redirectUrl: `${WEBHOST}/user/confirmation`,
				receiptButtonText: "Go to Dreampik",
				receiptLinkUrl: `${WEBHOST}`,
				receiptThankYouNote: "Thank you for your support! Your order is being processed and should be completed within a few minutes.",
			},
		});
		const checkoutUrl = checkout.data?.data.attributes.url;

		// Polar.sh
		// const polar = new Polar({
		// 	accessToken: process.env.POLAR_ACCESS_TOKEN ?? "",
		// 	server: process.env.NODE_ENV === "production" ? "production" : "sandbox",
		// });
		// const result = await polar.checkouts.create({
		// 	allowDiscountCodes: true,
		// 	products: [params.productId],
		// 	customerExternalId: user.id,
		// 	customerName: user.name,
		// 	customerEmail: user.email,
		// 	successUrl: `${WEBHOST}/user/confirmation`,
		// });
		// const checkoutUrl = result.url
		return NextResponse.json({ status: 200, url: checkoutUrl });
	} catch (error: any) {
		console.log(error);
		notifyDevEvent(`${WEBNAME} - api: /api/payment/checkout`, "Error", error.message, null);
		return NextResponse.json({ status: 500, message: error.message });
	}
}

// export const GET = Checkout({
// 	accessToken: process.env.POLAR_ACCESS_TOKEN!,
// 	successUrl: "/confirmation",
// 	server: process.env.NODE_ENV === "production" ? "production" : "sandbox", // Use this option if you're using the sandbox environment - else use 'production' or omit the parameter
// });
