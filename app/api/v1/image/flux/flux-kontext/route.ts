import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { getUUIDString } from "@/lib/utils";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { FLUX_1_KONTEXT_DEV, FLUX_1_KONTEXT_MAX, FLUX_1_KONTEXT_PRO } from "@/lib/utils-image-model";
import { saveToR2 } from "@/server/r2.server";
import { handleApiError } from "@/@types/error-api";
import { genFluxKontextDevFromFal, genFluxProKontextFromFal } from "@/server/ai/flux.server";
import { ParamsError } from "@/@types/error";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";

type Params = {
	model: string;
	prompt: string;
	image: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();
	if (!params.model || !params.prompt) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();

		const imageModel = [FLUX_1_KONTEXT_DEV, FLUX_1_KONTEXT_PRO, FLUX_1_KONTEXT_MAX].find((fluxModel) => fluxModel.id === params.model)!;
		if (process.env.NODE_ENV === "development") {
			console.log("imageModel: ", imageModel);
		}
		if (!imageModel) {
			throw new ParamsError("Image model is not found.");
		}
		const needCredits = imageModel.credits;
		const { creditConsumes, visibility } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});

		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
		}

		// track mixpanel event
		mixpanelTrackEvent(params.image ? "Gen Image to Image in Page" : "Gen Text to Image in Page", userId, {
			mp_country_code: cfIpCountryCode,
			modelId: imageModel.id,
			free: visibility,
		});

		const imageResultId = getUUIDString();
		let resultUrls: string[];
		switch (imageModel.model) {
			case FLUX_1_KONTEXT_DEV.model:
				resultUrls = await genFluxKontextDevFromFal(params.prompt, 1, params.image);
				break;
			case FLUX_1_KONTEXT_PRO.model:
			case FLUX_1_KONTEXT_MAX.model:
				resultUrls = await genFluxProKontextFromFal(imageModel.model, params.prompt, 1, undefined, params.image);
				break;
			default:
				throw new ParamsError("Image model is not found.");
		}
		if (process.env.NODE_ENV === "development") {
			console.log("resultUrls: ", resultUrls);
		}

		// save to r2
		const imagePaths = await Promise.all(resultUrls.map((url) => saveToR2(url)));

		// save to db
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					model: imageModel.id,
					prompt: params.prompt,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values(
				resultUrls.map((resultUrl, index) => ({
					uid: getUUIDString(),
					userId: userId,
					mediaHeadUid: media.uid,
					visibility: visibility,
					mediaPaths: imagePaths[index],
					mediaOriginUrls: resultUrl,
				})),
			);
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrls: imagePaths.map((path) => `${OSS_URL_HOST}${path}`) });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/image/flux/flux-kontext`, JSON.stringify(params));
	}
}
