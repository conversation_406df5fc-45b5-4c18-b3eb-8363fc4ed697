import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { getUUIDString } from "@/lib/utils";
import { WEBNAME } from "@/lib/constants";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { FLUX_1_1_PRO, FLUX_1_1_PRO_ULTRA, FLUX_1_KONTEXT_MAX, FLUX_1_KONTEXT_PRO, getFluxModel } from "@/lib/utils-image-model";
import { saveToR2 } from "@/server/r2.server";
import { handleApiError } from "@/@types/error-api";
import { genFluxFromWavespeed, genFluxProFromFal, genFluxProKontextFromFal } from "@/server/ai/flux.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";

type Params = {
	model: string;
	prompt: string;
	size: {
		ratio: string;
		width: number;
		height: number;
	};
	numImages: number;
	image?: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();
	if (!params.model || !params.prompt || !params.size || !params.numImages) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();

		const imageModel = getFluxModel(params.model);
		const needCredits = imageModel.credits * params.numImages;
		const { creditConsumes, visibility } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});

		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
		}

		// track mixpanel event
		mixpanelTrackEvent(params.image ? "Gen Image to Image in Page" : "Gen Text to Image in Page", userId, {
			mp_country_code: cfIpCountryCode,
			modelId: imageModel.id,
			free: visibility,
		});

		const imageResultId = getUUIDString();
		let resultUrls: string[];
		switch (imageModel.model) {
			case FLUX_1_1_PRO.model:
			case FLUX_1_1_PRO_ULTRA.model:
				resultUrls = await genFluxProFromFal(imageModel.model, params.prompt, params.numImages, params.size, params.image);
				break;
			case FLUX_1_KONTEXT_PRO.model:
			case FLUX_1_KONTEXT_MAX.model:
				resultUrls = await genFluxProKontextFromFal(imageModel.model, params.prompt, params.numImages, params.size, params.image);
				break;
			default:
				// flux fast, flux dev
				resultUrls = await genFluxFromWavespeed(imageModel.model, params.prompt, params.numImages, params.size, params.image);
				break;
		}

		// save to r2
		const imagePaths = await Promise.all(resultUrls.map((url) => saveToR2(url)));

		// save to db
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					model: imageModel.id,
					prompt: params.prompt,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values(
				resultUrls.map((resultUrl, index) => ({
					uid: getUUIDString(),
					userId: userId,
					mediaHeadUid: media.uid,
					visibility: visibility,
					mediaPaths: imagePaths[index],
					mediaOriginUrls: resultUrl,
				})),
			);
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrls: resultUrls });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/image/flux`);
	}
}
