import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { getUUIDString } from "@/lib/utils";
import { WEBNAME } from "@/lib/constants";
import { ofetch } from "ofetch";
import { getUserRealtime } from "@/server/utils-user.server";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { GEMINI_FLASH } from "@/lib/utils-image-model";
import { saveToR2 } from "@/server/r2.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";

type Params = {
	images: string[];
	prompt: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");

	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const userId = sessionUser.id;

	const params: Params = await req.json();
	// console.log("params: ", params);
	if (!params.images || !params.prompt) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	// 2 查询用户最新数据（需要用到credits）
	const needCredits = GEMINI_FLASH.credits;
	if (!needCredits) return NextResponse.json({ status: 400, message: "Model is not found." });
	const user = await getUserRealtime(userId);
	if (!user) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const { isValid, creditConsumes, isUserNotFound, visibility } = await checkUserCredit(userId, {
		needCredits: needCredits,
		existUser: user,
	});
	if (isUserNotFound) return NextResponse.json({ status: 401, message: "Not authorized." });
	if (!isValid) {
		return NextResponse.json({ status: 402, message: "You have not enough credits." });
	}

	// 3. track mixpanel event
	// mixpanelTrackEvent("generate-logo", userId, {
	// 	mp_country_code: cfIpCountryCode,
	// });

	let payload: any = {
		prompt: params.prompt,
	};
	if (params.images.length === 1) {
		payload = {
			...payload,
			image_url: params.images[0],
		};
	} else {
		payload = {
			...payload,
			input_image_urls: params.images,
		};
	}
	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
		console.log("creditConsumes: ", creditConsumes);
		console.log("payload: ", payload);
	}
	// return NextResponse.json({ status: 500, message: "error" });

	// track mixpanel event
	mixpanelTrackEvent("Gen Image to Image in Page", userId, {
		mp_country_code: cfIpCountryCode,
		modelId: GEMINI_FLASH.id,
		free: visibility,
	});

	try {
		const imageResultId = getUUIDString();
		const { request_id } = await ofetch(`https://queue.fal.run/fal-ai/gemini-flash-edit${params.images.length === 1 ? "" : "/multi"}`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Key ${process.env.FAL_API_KEY}`,
			},
			body: payload,
		});
		const requestId = request_id;

		let resultUrl = null;
		await new Promise((resolve) => setTimeout(resolve, 7000)); // wait for 7 seconds
		while (true) {
			const { status } = await ofetch(`https://queue.fal.run/fal-ai/gemini-flash-edit/requests/${requestId}/status`, {
				headers: {
					Authorization: `Key ${process.env.FAL_API_KEY}`,
				},
			});
			// status: IN_QUEUE, IN_PROGRESS, COMPLETED
			if (status === "COMPLETED") {
				const { image } = await ofetch(`https://queue.fal.run/fal-ai/gemini-flash-edit/requests/${requestId}`, {
					headers: {
						Authorization: `Key ${process.env.FAL_API_KEY}`,
					},
				});
				resultUrl = image.url;
				break;
			} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
			} else {
				throw new Error(`Failed to generate image.`);
			}

			await new Promise((resolve) => setTimeout(resolve, 2000)); // wait for 2 seconds
		}
		// save to r2
		const imagePath = await saveToR2(resultUrl);

		// save to db
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					model: GEMINI_FLASH.id,
					prompt: params.prompt,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values({
				uid: getUUIDString(),
				userId: userId,
				mediaHeadUid: media.uid,
				visibility: visibility,
				mediaPaths: imagePath,
				mediaOriginUrls: resultUrl,
			});
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrl: resultUrl });
	} catch (error: any) {
		console.error(error);
		notifyDevEvent(`${WEBNAME} - /api/v1/image/gemini-flash`, "Error", error.message, null);
		return NextResponse.json({ status: 500, error: error.message });
	}
}
