import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { getUUIDString } from "@/lib/utils";
import { WEBNAME } from "@/lib/constants";
import { ofetch } from "ofetch";
import { getUserRealtime } from "@/server/utils-user.server";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { HIDREAM_EI_FULL } from "@/lib/utils-image-model";
import { saveToR2 } from "@/server/r2.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";

type Params = {
	image: string;
	prompt: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");

	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const userId = sessionUser.id;

	const params: Params = await req.json();
	// console.log("params: ", params);
	if (!params.image || !params.prompt) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	// 2 查询用户最新数据（需要用到credits）
	const needCredits = HIDREAM_EI_FULL.credits;
	if (!needCredits) return NextResponse.json({ status: 400, message: "Model is not found." });
	const user = await getUserRealtime(userId);
	if (!user) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const { isValid, creditConsumes, isUserNotFound, visibility } = await checkUserCredit(userId, {
		needCredits: needCredits,
		existUser: user,
	});
	if (isUserNotFound) return NextResponse.json({ status: 401, message: "Not authorized." });
	if (!isValid) {
		return NextResponse.json({ status: 402, message: "You have not enough credits." });
	}
	// console.log("model: ", params.model);
	// console.log("creditConsumes: ", creditConsumes);
	// console.log("payload: ", payload);
	// return NextResponse.json({ status: 500, message: "error" });

	// track mixpanel event
	mixpanelTrackEvent("Gen Image to Image in Page", userId, {
		mp_country_code: cfIpCountryCode,
		modelId: HIDREAM_EI_FULL.id,
		free: visibility,
	});

	try {
		const imageResultId = getUUIDString();

		const payload = {
			enable_base64_output: false,
			enable_safety_checker: true,
			image: params.image,
			prompt: params.prompt,
			seed: -1,
		};

		// https://docs.kie.ai/api/create-4-o-image-task/
		const { code, message, data } = await ofetch("https://api.wavespeed.ai/api/v2/wavespeed-ai/hidream-e1-full", {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
			},
			body: payload,
		});
		if (code !== 200) {
			throw new Error(`Failed to generate image. ${message}`);
		}
		// console.log("data: ", data);
		const requestId = data.id;
		const getUrl = data.urls.get;

		let resultUrl = null;
		await new Promise((resolve) => setTimeout(resolve, 4000)); // wait for 5 seconds
		while (true) {
			const {
				code: resultCode,
				message: resultMessage,
				data: resutlData,
			} = await ofetch(`https://api.wavespeed.ai/api/v2/predictions/${requestId}/result`, {
				headers: {
					Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
				},
			});
			if (resultCode !== 200) {
				console.error(`Error: ${resultCode}, ${resultMessage}`);
				throw new Error(`Failed to get image result. ${resultMessage}`);
			}
			const { status, outputs, error } = resutlData;
			// status: processing, completed, failed
			if (status === "completed") {
				resultUrl = outputs[0];
				// console.log("Task completed. URL:", resultUrl);
				break;
			} else if (status === "failed") {
				console.error("Task failed:", error);
				throw new Error(`Failed to generate image. Result: ${error}`);
			} else {
				// console.log("Task still processing. Status:", status);
			}

			await new Promise((resolve) => setTimeout(resolve, 2500)); // wait for 2.5 seconds
		}

		// save to r2
		const imagePath = await saveToR2(resultUrl);

		// save to db
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					model: HIDREAM_EI_FULL.id,
					prompt: params.prompt,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values({
				uid: getUUIDString(),
				userId: userId,
				mediaHeadUid: media.uid,
				visibility: visibility,
				mediaPaths: imagePath,
				mediaOriginUrls: resultUrl,
			});
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrl: resultUrl });
	} catch (error: any) {
		console.error(error);
		notifyDevEvent(`${WEBNAME} - /api/v1/image/hidream-e1-full`, "Error", error.message, null);
		return NextResponse.json({ status: 500, error: error.message });
	}
}
