import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { getUUIDString } from "@/lib/utils";
import { WEBNAME } from "@/lib/constants";
import { ofetch } from "ofetch";
import { getUserRealtime } from "@/server/utils-user.server";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { getHiDreamModel } from "@/lib/utils-image-model";
import { saveToR2 } from "@/server/r2.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";

type Params = {
	model: string;
	prompt: string;
	size: {
		width: number;
		height: number;
	};
	image?: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");

	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const userId = sessionUser.id;

	const params: Params = await req.json();
	// console.log("params: ", params);
	if (!params.model || !params.prompt || !params.size) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	// 2 查询用户最新数据（需要用到credits）
	const imageModel = getHiDreamModel(params.model);
	const needCredits = imageModel.credits;
	if (!needCredits) return NextResponse.json({ status: 400, message: "Model is not found." });
	const user = await getUserRealtime(userId);
	if (!user) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const { isValid, creditConsumes, isUserNotFound, visibility } = await checkUserCredit(userId, {
		needCredits: needCredits,
		existUser: user,
	});
	if (isUserNotFound) return NextResponse.json({ status: 401, message: "Not authorized." });
	if (!isValid) {
		return NextResponse.json({ status: 402, message: "You have not enough credits." });
	}

	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
		console.log("creditConsumes: ", creditConsumes);
	}

	// track mixpanel event
	mixpanelTrackEvent(params.image ? "Gen Image to Image in Page" : "Gen Text to Image in Page", userId, {
		mp_country_code: cfIpCountryCode,
		modelId: imageModel.id,
		free: visibility,
	});

	try {
		const imageResultId = getUUIDString();

		let resultUrl = null;
		if (params.model === "hidream-i1-full" && params.image) {
			resultUrl = await generateImageFromFal(params.prompt, 1, params.image, params.size);
		} else {
			resultUrl = await generateImageFromWavespeed(params.model, params.prompt, params.size);
		}
		if (!resultUrl) {
			throw new Error("Failed to generate image.");
		}

		// save to r2
		const imagePath = await saveToR2(resultUrl);

		// save to db
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					model: imageModel.id,
					prompt: params.prompt,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values({
				uid: getUUIDString(),
				userId: userId,
				mediaHeadUid: media.uid,
				visibility: visibility,
				mediaPaths: imagePath,
				mediaOriginUrls: resultUrl,
			});
		});

		// 更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrl: resultUrl });
	} catch (error: any) {
		console.error(error);
		notifyDevEvent(`${WEBNAME} - /api/v1/image/hidream-i1`, "Error", error.message, null);
		return NextResponse.json({ status: 500, error: error.message });
	}
}

async function generateImageFromWavespeed(
	model: string,
	prompt: string,
	size: {
		width: number;
		height: number;
	},
): Promise<string | null> {
	const payload = {
		enable_base64_output: false,
		enable_safety_checker: true,
		prompt: prompt,
		seed: -1,
		size: `${size.width}*${size.height}`,
	};

	if (process.env.NODE_ENV === "development") {
		console.log("wavespeed payload: ", payload);
	}
	// throw new Error("wavespeed test error");

	const { code, message, data } = await ofetch(`https://api.wavespeed.ai/api/v2/wavespeed-ai/${model}`, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
		},
		body: payload,
	});
	if (code !== 200) {
		throw new Error(`Failed to generate image. ${message}`);
	}
	// console.log("data: ", data);
	const requestId = data.id;
	const getUrl = data.urls.get;

	let resultUrl = null;
	await new Promise((resolve) => setTimeout(resolve, 3000)); // wait for 3 seconds
	while (true) {
		const {
			code: resultCode,
			message: resultMessage,
			data: resutlData,
		} = await ofetch(`https://api.wavespeed.ai/api/v2/predictions/${requestId}/result`, {
			headers: {
				Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
			},
		});
		if (resultCode !== 200) {
			console.error(`Error: ${resultCode}, ${resultMessage}`);
			throw new Error(`Failed to get image result. ${resultMessage}`);
		}
		const { status, outputs, error } = resutlData;
		// status: processing, completed, failed
		if (status === "completed") {
			resultUrl = outputs[0];
			// console.log("Task completed. URL:", resultUrl);
			break;
		} else if (status === "failed") {
			console.error("Task failed:", error);
			throw new Error(`Failed to generate image. Result: ${error}`);
		} else {
			// console.log("Task still processing. Status:", status);
		}

		await new Promise((resolve) => setTimeout(resolve, 1500)); // wait for 1.5 seconds
	}
	return resultUrl;
}

async function generateImageFromFal(
	prompt: string,
	numImages: number,
	imageUrl: string | null | undefined,
	size: {
		width: number;
		height: number;
	},
): Promise<string | null> {
	let payload: any = {
		prompt: prompt,
		num_images: numImages,
		image_size: {
			width: size.width,
			height: size.height,
		},
	};
	if (imageUrl) {
		payload.image_url = imageUrl;
	}

	if (process.env.NODE_ENV === "development") {
		console.log("fal payload: ", payload);
	}
	// throw new Error("fal test error");

	const { request_id } = await ofetch(`https://queue.fal.run/fal-ai/hidream-i1-full/image-to-image`, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Key ${process.env.FAL_API_KEY}`,
		},
		body: payload,
	});
	const requestId = request_id;

	let resultUrl = null;
	await new Promise((resolve) => setTimeout(resolve, 5000)); // wait for 5 seconds
	while (true) {
		const { status } = await ofetch(`https://queue.fal.run/fal-ai/hidream-i1-full/requests/${requestId}/status`, {
			headers: {
				Authorization: `Key ${process.env.FAL_API_KEY}`,
			},
		});
		// status: IN_QUEUE, IN_PROGRESS, COMPLETED
		if (status === "COMPLETED") {
			const { images } = await ofetch(`https://queue.fal.run/fal-ai/hidream-i1-full/requests/${requestId}`, {
				headers: {
					Authorization: `Key ${process.env.FAL_API_KEY}`,
				},
			});
			resultUrl = images[0].url;
			break;
		} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
		} else {
			throw new Error(`Failed to generate image.`);
		}

		await new Promise((resolve) => setTimeout(resolve, 2500)); // wait for 2.5 seconds
	}
	return resultUrl;
}
