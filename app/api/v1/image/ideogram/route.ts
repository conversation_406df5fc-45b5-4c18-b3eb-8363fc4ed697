import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { getUUIDString } from "@/lib/utils";
import { WEBNAME } from "@/lib/constants";
import { ofetch } from "ofetch";
import { getUserRealtime } from "@/server/utils-user.server";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { ideogramCredits } from "@/lib/utils-image-model";
import { saveToR2 } from "@/server/r2.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";

type Params = {
	prompt: string;
	size: {
		width: string;
		height: string;
	};
	renderSpeed: string;
	numImages: number;
	style: string | null | undefined;
	image: string | null | undefined;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");

	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const userId = sessionUser.id;

	const params: Params = await req.json();
	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
	}
	if (!params.prompt || !params.size || !params.renderSpeed || !params.numImages) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	// 2 查询用户最新数据（需要用到credits）
	const needCredits = ideogramCredits(params.numImages, params.renderSpeed);
	if (!needCredits) return NextResponse.json({ status: 400, message: "Model is not found." });
	const user = await getUserRealtime(userId);
	if (!user) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const { isValid, creditConsumes, isUserNotFound, visibility } = await checkUserCredit(userId, {
		needCredits: needCredits,
		existUser: user,
	});
	if (isUserNotFound) return NextResponse.json({ status: 401, message: "Not authorized." });
	if (!isValid) {
		return NextResponse.json({ status: 402, message: "You have not enough credits." });
	}

	// track mixpanel event
	mixpanelTrackEvent(params.image ? "Gen Image to Image in Page" : "Gen Text to Image in Page", userId, {
		mp_country_code: cfIpCountryCode,
		modelId: `ideogram-3-${params.renderSpeed.toLowerCase()}`,
		free: visibility,
	});

	let payload: any = {
		prompt: params.prompt,
		rendering_speed: params.renderSpeed,
		num_images: params.numImages,
		image_size: {
			width: params.size.width,
			height: params.size.height,
		},
	};
	if (params.style) {
		payload.style = params.style;
	}
	if (params.image) {
		payload.image_urls = [params.image];
	}

	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
		console.log("creditConsumes: ", creditConsumes);
		console.log("payload: ", payload);
	}
	// return NextResponse.json({ status: 500, message: "error" });

	try {
		const imageResultId = getUUIDString();
		const { request_id } = await ofetch(`https://queue.fal.run/fal-ai/ideogram/v3`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Key ${process.env.FAL_API_KEY}`,
			},
			body: payload,
		});
		const requestId = request_id;

		let resultUrls: string[] = [];
		await new Promise((resolve) => setTimeout(resolve, 9000)); // wait for 9 seconds
		while (true) {
			const { status } = await ofetch(`https://queue.fal.run/fal-ai/ideogram/requests/${requestId}/status`, {
				headers: {
					Authorization: `Key ${process.env.FAL_API_KEY}`,
				},
			});
			// status: IN_QUEUE, IN_PROGRESS, COMPLETED
			if (status === "COMPLETED") {
				const { images } = await ofetch(`https://queue.fal.run/fal-ai/ideogram/requests/${requestId}`, {
					headers: {
						Authorization: `Key ${process.env.FAL_API_KEY}`,
					},
				});
				resultUrls = images.map((image: any) => image.url);
				break;
			} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
			} else {
				throw new Error(`Failed to generate image.`);
			}

			await new Promise((resolve) => setTimeout(resolve, 2000)); // wait for 2 seconds
		}
		// save to r2
		const imagePaths = await Promise.all(resultUrls.map((url) => saveToR2(url)));

		// save to db
		const db = getDB();

		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					model: `ideogram-3-${params.renderSpeed.toLowerCase()}`,
					prompt: params.prompt,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values(
				resultUrls.map((resultUrl, index) => ({
					uid: getUUIDString(),
					userId: userId,
					mediaHeadUid: media.uid,
					visibility: visibility,
					mediaPaths: imagePaths[index],
					mediaOriginUrls: resultUrl,
				})),
			);
		});

		// 更新用户credits
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrls: resultUrls });
	} catch (error: any) {
		console.error(error);
		notifyDevEvent(`${WEBNAME} - /api/v1/image/ideogram`, "Error", error.message, null);
		return NextResponse.json({ status: 500, error: error.message });
	}
}
