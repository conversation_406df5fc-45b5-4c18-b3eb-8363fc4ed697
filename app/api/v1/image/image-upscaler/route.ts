import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { getUUIDString } from "@/lib/utils";
import { WEBNAME } from "@/lib/constants";
import { ofetch } from "ofetch";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { saveToR2 } from "@/server/r2.server";
import { handleApiError } from "@/@types/error-api";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";

type Params = {
	image: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");

	const params: Params = await req.json();
	if (!params.image) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();

		const needCredits = 1;
		const { creditConsumes, visibility } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});

		const payload: any = {
			image_url: params.image,
			enable_safety_checker: false,
		};
		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
			console.log("payload: ", payload);
		}

		// track mixpanel event
		mixpanelTrackEvent("Upscale Image", userId, {
			mp_country_code: cfIpCountryCode,
			free: visibility,
		});

		const imageResultId = getUUIDString();
		const { request_id } = await ofetch(`https://queue.fal.run/fal-ai/recraft/upscale/crisp`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Key ${process.env.FAL_API_KEY}`,
			},
			body: payload,
		});
		const requestId = request_id;

		let resultUrl = null;
		await new Promise((resolve) => setTimeout(resolve, 10)); // wait for 10 seconds
		while (true) {
			const { status } = await ofetch(`https://queue.fal.run/fal-ai/recraft/requests/${requestId}/status`, {
				headers: {
					Authorization: `Key ${process.env.FAL_API_KEY}`,
				},
			});
			// status: IN_QUEUE, IN_PROGRESS, COMPLETED
			if (status === "COMPLETED") {
				const { image } = await ofetch(`https://queue.fal.run/fal-ai/recraft/requests/${requestId}`, {
					headers: {
						Authorization: `Key ${process.env.FAL_API_KEY}`,
					},
				});
				resultUrl = image.url;
				break;
			} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
			} else {
				throw new Error(`Failed to generate image.`);
			}

			await new Promise((resolve) => setTimeout(resolve, 2000)); // wait for 2 seconds
		}
		// save to r2
		const imagePath = await saveToR2(resultUrl);

		// save to db
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					model: "image-upscaler",
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values({
				uid: getUUIDString(),
				userId: userId,
				mediaHeadUid: media.uid,
				visibility: visibility,
				mediaPaths: imagePath,
				mediaOriginUrls: resultUrl,
			});
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrl: resultUrl });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/image/image-upscaler`);
	}
}
