import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { getUUIDString } from "@/lib/utils";
import { WEBNAME } from "@/lib/constants";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { saveToR2 } from "@/server/r2.server";
import { handleApiError } from "@/@types/error-api";
import { IMAGEN_3_FAST, IMAGEN_4_PREVIEW, IMAGEN_4_PREVIEW_FAST, IMAGEN_4_PREVIEW_ULTRA } from "@/lib/utils-image-model";
import { ParamsError, ServerError } from "@/@types/error";
import { genGoogleImagen3FromFal, genGoogleImagen4FromFal } from "@/server/ai/imagen.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";

type Params = {
	model: string;
	prompt: string;
	size: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");

	const params: Params = await req.json();
	if (!params.model || !params.prompt || !params.size) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	const imagenModels = [IMAGEN_4_PREVIEW, IMAGEN_3_FAST];
	try {
		const userId = await getSessionUserId();

		const imageModel = imagenModels.find((textToImageModel) => textToImageModel.id === params.model);
		if (!imageModel) {
			throw new ParamsError("Image model is not found.");
		}
		const needCredits = imageModel.credits;
		const { creditConsumes, visibility } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});

		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
		}

		// track mixpanel event
		mixpanelTrackEvent("Gen Text to Image in Page", userId, {
			mp_country_code: cfIpCountryCode,
			modelId: imageModel.id,
			free: visibility,
		});

		let resultUrls: string[];
		switch (imageModel.model) {
			case IMAGEN_4_PREVIEW.model:
			case IMAGEN_4_PREVIEW_FAST.model:
			case IMAGEN_4_PREVIEW_ULTRA.model:
				resultUrls = await genGoogleImagen4FromFal(imageModel.model, params.prompt, 1, params.size);
				break;
			case IMAGEN_3_FAST.model:
				resultUrls = await genGoogleImagen3FromFal(imageModel.model, params.prompt, 1, params.size);
				break;
			default:
				throw new ParamsError("Image model is not found.");
				break;
		}
		if (resultUrls.length === 0) {
			throw new ServerError("Image generation failed.");
		}

		// save to r2
		const imagePaths = await Promise.all(resultUrls.map((url) => saveToR2(url)));

		// save to db
		const imageResultId = getUUIDString();
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					model: imageModel.id,
					prompt: params.prompt,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values(
				resultUrls.map((resultUrl, index) => ({
					uid: getUUIDString(),
					userId: userId,
					mediaHeadUid: media.uid,
					visibility: visibility,
					mediaPaths: imagePaths[index],
					mediaOriginUrls: resultUrl,
				})),
			);
		});

		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrls: resultUrls });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/image/imagen`);
	}
}
