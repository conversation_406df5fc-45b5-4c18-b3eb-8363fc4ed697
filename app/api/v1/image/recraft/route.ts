import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { notifyDevEvent } from "@/server/dev-notify.server";
import { getUUIDString } from "@/lib/utils";
import { WEBNAME } from "@/lib/constants";
import { ofetch } from "ofetch";
import { getUserRealtime } from "@/server/utils-user.server";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { getRecraftCredits, RECRAFT_3 } from "@/lib/utils-image-model";
import { saveToR2 } from "@/server/r2.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";

type Params = {
	prompt: string;
	size: {
		width: string;
		height: string;
	};
	style: string;
	image: string | null | undefined;
	similar: number;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	// const cfIp = req.headers.get("cf-connecting-ip");

	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const userId = sessionUser.id;

	const params: Params = await req.json();
	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
	}
	if (!params.prompt || !params.size || !params.style) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	// 2 查询用户最新数据（需要用到credits）
	const needCredits = getRecraftCredits(params.style);
	if (!needCredits) return NextResponse.json({ status: 400, message: "Model is not found." });
	const user = await getUserRealtime(userId);
	if (!user) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const { isValid, creditConsumes, isUserNotFound, visibility } = await checkUserCredit(userId, {
		needCredits: needCredits,
		existUser: user,
	});
	if (isUserNotFound) return NextResponse.json({ status: 401, message: "Not authorized." });
	if (!isValid) {
		return NextResponse.json({ status: 402, message: "You have not enough credits." });
	}

	let payload: any = {
		prompt: params.prompt,
		style: params.style,
	};
	if (params.image) {
		payload.image_url = params.image;
		payload.strength = Number((1 - params.similar).toFixed(2));
	} else {
		payload.image_size = {
			width: params.size.width,
			height: params.size.height,
		};
	}

	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
		console.log("creditConsumes: ", creditConsumes);
		console.log("payload: ", payload);
	}
	// return NextResponse.json({ status: 500, message: "error" });

	// track mixpanel event
	mixpanelTrackEvent(params.image ? "Gen Image to Image in Page" : "Gen Text to Image in Page", userId, {
		mp_country_code: cfIpCountryCode,
		modelId: RECRAFT_3.id,
		free: visibility,
	});

	try {
		const imageResultId = getUUIDString();
		const { request_id } = await ofetch(`https://queue.fal.run/fal-ai/recraft/v3${params.image ? "image-to-image" : "text-to-image"}`, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Key ${process.env.FAL_API_KEY}`,
			},
			body: payload,
		});
		const requestId = request_id;

		let resultUrl: string | null = null;
		await new Promise((resolve) => setTimeout(resolve, 9000)); // wait for 9 seconds
		while (true) {
			const { status } = await ofetch(`https://queue.fal.run/fal-ai/recraft/requests/${requestId}/status`, {
				headers: {
					Authorization: `Key ${process.env.FAL_API_KEY}`,
				},
			});
			// status: IN_QUEUE, IN_PROGRESS, COMPLETED
			if (status === "COMPLETED") {
				const { images } = await ofetch(`https://queue.fal.run/fal-ai/recraft/requests/${requestId}`, {
					headers: {
						Authorization: `Key ${process.env.FAL_API_KEY}`,
					},
				});
				resultUrl = images[0].url;
				break;
			} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
			} else {
				throw new Error(`Failed to generate image.`);
			}

			await new Promise((resolve) => setTimeout(resolve, 2000)); // wait for 2 seconds
		}
		// save to r2
		const imagePath = await saveToR2(resultUrl!);

		// save to db
		const db = getDB();
		await db.transaction(async (tx) => {
			const [media]: MediaHead[] = await tx
				.insert(mediaHeadSchema)
				.values({
					uid: imageResultId,
					userId: userId,
					model: RECRAFT_3.id,
					prompt: params.prompt,
					status: MediaResultStatus.Completed,
					creditsSources: JSON.stringify(creditConsumes),
				})
				.returning();
			await tx.insert(mediaItemSchema).values({
				uid: getUUIDString(),
				userId: userId,
				mediaHeadUid: media.uid,
				visibility: visibility,
				mediaPaths: imagePath,
				mediaOriginUrls: resultUrl,
			});
		});

		// 更新用户credits
		await updateUserCredit(userId, creditConsumes, {
			remark: `Image task id: ${imageResultId}.`,
		});

		return NextResponse.json({ message: "Success", resultUrls: [resultUrl] });
	} catch (error: any) {
		console.error(error);
		notifyDevEvent(`${WEBNAME} - /api/v1/image/recraft`, "Error", error.message, null);
		return NextResponse.json({ status: 500, error: error.message });
	}
}
