import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { WEBNAME } from "@/lib/constants";
import { mediaTaskSchema } from "@/server/db/schema.server";
import { MediaHeadType, MediaResultStatus } from "@/@types/media/media-type";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { handleApiError } from "@/@types/error-api";
import {
	getVideoModel,
	getVideoModelCredits,
	GOOGLE_VEO_3,
	HAILUO_2_PRO,
	HAILUO_2_STANDARD,
	KLING_1_6_STANDARD,
	KLING_2_1_MASTER,
	KLING_2_1_PRO,
	KLING_2_1_STANDARD,
	KLING_2_MASTER,
	LTX_13B_DISTILLED,
	PIXVERSE_4_5,
	PIXVERSE_4_5_FAST,
	SEEDANCE_1_LITE,
	SEEDANCE_1_PRO,
} from "@/lib/utils-video-model";
import { genVeoFromFal } from "@/server/ai/veo.server";
import {
	genKling2FromFal,
	genKling2Point1MasterFromFal,
	genKling1Point6FromFal,
	genKling2Point1ProFromFal,
	genKling2Point1StandardFromFal,
} from "@/server/ai/kling.server";
import { genLTXFromFal } from "@/server/ai/ltx.server";
import { genSeedance1LiteFromFal, genSeedance1ProFromFal } from "@/server/ai/seedance.server";
import { genPixverse4Point5FastFromFal, genPixverse4Point5FromFal } from "@/server/ai/pixverse.server";
import { genHailuo2ProFromFal, genHailuo2StandardFromFal } from "@/server/ai/hailuo.server";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";

type Params = {
	model: string;
	prompt: string;
	image?: string;
};

export async function POST(req: Request) {
	// return NextResponse.json({ message: "Success", task_status: MediaResultStatus.InProgress, request_id: "965ab039-4902-4cca-ad93-b1a40dbe64de" });
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();
	if (!params.model || !params.prompt) {
		return NextResponse.json({ status: 400, message: "Parameters is missing." });
	}

	try {
		const userId = await getSessionUserId();

		const videoModel = getVideoModel(params.model);
		const duration = videoModel.durationAll[0];
		const aspectRatio = videoModel.aspectRatioAll ? videoModel.aspectRatioAll[0] : null;
		const resolution = videoModel.resolutionAll ? videoModel.resolutionAll[0] : null;
		const needCredits = getVideoModelCredits(videoModel, duration, resolution);
		const { creditConsumes, visibility } = await checkUserCredit(userId, {
			needCredits: needCredits,
		});

		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("duration: ", duration);
			console.log("aspectRatio: ", aspectRatio);
			console.log("resolution: ", resolution);
			console.log("creditConsumes: ", creditConsumes);
		}

		// track mixpanel event
		mixpanelTrackEvent(params.image ? "Gen Image to Video in Page" : "Gen Text to Video in Page", userId, {
			mp_country_code: cfIpCountryCode,
			modelId: videoModel.id,
			free: visibility,
		});

		let requestId: string;
		switch (videoModel.model) {
			case GOOGLE_VEO_3.model:
				requestId = await genVeoFromFal(videoModel.model, params.prompt, duration, aspectRatio!);
				break;
			case KLING_2_1_MASTER.model:
				requestId = await genKling2Point1MasterFromFal(videoModel.model, params.prompt, duration, aspectRatio!, params.image);
				break;
			case KLING_2_1_PRO.model:
				requestId = await genKling2Point1ProFromFal(videoModel.model, params.prompt, duration, aspectRatio!, params.image!);
				break;
			case KLING_2_1_STANDARD.model:
				requestId = await genKling2Point1StandardFromFal(videoModel.model, params.prompt, duration, aspectRatio!, params.image!);
				break;
			case KLING_2_MASTER.model:
				requestId = await genKling2FromFal(videoModel.model, params.prompt, duration, aspectRatio!);
				break;
			case KLING_1_6_STANDARD.model:
				requestId = await genKling1Point6FromFal(videoModel.model, params.prompt, duration, aspectRatio!);
				break;
			case LTX_13B_DISTILLED.model:
				requestId = await genLTXFromFal(videoModel.model, params.prompt, aspectRatio!, params.image);
				break;
			case SEEDANCE_1_LITE.model:
				requestId = await genSeedance1LiteFromFal(videoModel.model, params.prompt, duration, aspectRatio!, params.image);
				break;
			case SEEDANCE_1_PRO.model:
				requestId = await genSeedance1ProFromFal(videoModel.model, params.prompt, duration, aspectRatio!, params.image);
				break;
			case PIXVERSE_4_5.model:
				requestId = await genPixverse4Point5FromFal(videoModel.model, params.prompt, duration, aspectRatio!, resolution!, null, params.image);
				break;
			case PIXVERSE_4_5_FAST.model:
				requestId = await genPixverse4Point5FastFromFal(videoModel.model, params.prompt, aspectRatio!, resolution!, null, params.image);
				break;
			case HAILUO_2_STANDARD.model:
				requestId = await genHailuo2StandardFromFal(videoModel.model, params.prompt, duration, params.image);
				break;
			case HAILUO_2_PRO.model:
				requestId = await genHailuo2ProFromFal(videoModel.model, params.prompt, params.image);
				break;
			default:
				throw new Error("Invalid model.");
		}

		// save to db
		const db = getDB();
		await db.insert(mediaTaskSchema).values({
			userId: userId,
			type: MediaHeadType.Video,
			model: videoModel.id,
			modelName: videoModel.name,
			prompt: params.prompt,
			videoDuration: duration,
			aspectRatio: aspectRatio,
			requestId: requestId,
			status: MediaResultStatus.InProgress,
			visibility: visibility,
			creditsSources: JSON.stringify(creditConsumes),
			ip: cfIp,
		});
		// //更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Video task request id: ${requestId}.`,
		});

		return NextResponse.json({ message: "Success", task_status: MediaResultStatus.InProgress, request_id: requestId });
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/video/page-video`);
	}
}
