import { MetadataRoute } from "next";
// import { i18nConfig } from "@/i18n-config";
import { WEBHOST } from "@/lib/constants";

export const dynamic = "force-static";

interface SitemapEntry {
	url: string;
	lastModified?: Date;
	changeFrequency?: "monthly" | "daily" | "always" | "hourly" | "weekly" | "yearly" | "never";
	priority?: number;
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	const baseUrl = WEBHOST;

	return [
		{ url: baseUrl },
		{ url: baseUrl + "/terms-of-use" },
		{ url: baseUrl + "/privacy-policy" },
		{ url: baseUrl + "/changelog" },
		{ url: baseUrl + "/pricing" },
		{ url: baseUrl + "/news" },
	] as SitemapEntry[];

	// //legal terms
	// const terms: SitemapEntry[] = [{ url: baseUrl + "/terms-of-use" }, { url: baseUrl + "/privacy-policy" }];

	// //by locale
	// const localePages: () => SitemapEntry[] = () => {
	// 	let site: SitemapEntry[] = [];
	// 	i18nConfig.locales.map((locale) => {
	// 		let baseLocaleUrl = baseUrl;
	// 		if (locale !== i18nConfig.defaultLocale) {
	// 			baseLocaleUrl += "/" + locale;
	// 		}
	// 		site.push(
	// 			...[
	// 				{ url: baseLocaleUrl },
	// 				// { url: baseLocaleUrl + "/changelog" },
	// 				// { url: baseLocaleUrl + "/pricing" }
	// 			],
	// 		);
	// 	});
	// 	return site;
	// };

	// return [...localePages(), ...terms];
}
