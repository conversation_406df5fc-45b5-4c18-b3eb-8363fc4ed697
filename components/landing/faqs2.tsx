type Faq = {
	question: string;
	answer: string;
};

export function FAQs2Component({ title, faqs }: { title?: string; faqs: Faq[] }) {
	return (
		<div id="faq" className="py-20">
			<div className="container flex flex-col items-center justify-center gap-16 px-6">
				<div className="max-w-4xl text-center">
					<h2 className="text-[32px] font-medium text-pretty">{title ?? "Frequently asked questions"}</h2>
				</div>
				<div className="w-full columns-1 space-y-4 sm:columns-2">
					{faqs.map((faq, index) => (
						<div key={index} className="bg-muted flex w-full flex-col items-start gap-3 overflow-hidden rounded-xl p-4 sm:p-8">
							<div className="text-start text-lg font-medium hover:no-underline">{faq.question}</div>
							<div className="text-muted-foreground font-[370] whitespace-pre-wrap">{faq.answer}</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}
