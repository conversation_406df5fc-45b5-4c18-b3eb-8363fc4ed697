"use client";

import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { Comparison, ComparisonHandle, ComparisonItem } from "@/components/ui/kibo-ui/comparison";

type Feature = {
	title: string;
	description: string;
	beforeSrc: string;
	afterSrc: string;
};

export default function FeaturesComparisonComponent({ ctaText, ctaUrl, features }: { ctaText: string; ctaUrl: string; features: Feature[] }) {
	return (
		<>
			{features.map((feature, index) => (
				<div key={index} className="container flex flex-col items-center gap-16 px-6 py-20">
					<div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 lg:gap-16">
						<div className={cn("flex h-full flex-col items-start justify-between gap-4", index % 2 === 0 ? "md:order-last" : "")}>
							<div className="flex flex-col gap-2">
								<h2 className="text-[32px] font-semibold text-balance">{feature.title}</h2>
								<p className="text-muted-foreground text-base">{feature.description}</p>
							</div>
							<NoPrefetchLink href={ctaUrl} className={cn(buttonVariants({ size: "lg" }), "h-11 rounded-full")}>
								{ctaText}
							</NoPrefetchLink>
						</div>
						<div className={cn("block w-full")}>
							<Comparison className="aspect-[3/2] min-w-[280px] rounded-lg md:min-w-[318px]">
								<ComparisonItem position="right" className="">
									<div className="relative aspect-[3/2]">
										<img
											src={feature.beforeSrc}
											alt={`${feature.title}: before`}
											className="h-full w-full rounded-lg object-cover"
											loading="lazy"
										/>
									</div>
								</ComparisonItem>
								<ComparisonItem position="left" className="">
									<div className="relative aspect-[3/2]">
										<img
											src={feature.afterSrc}
											alt={`${feature.title}: after`}
											className="h-full w-full rounded-lg object-cover"
											loading="lazy"
										/>
									</div>
								</ComparisonItem>
								<ComparisonHandle />
							</Comparison>
						</div>
					</div>
				</div>
			))}
		</>
	);
}
