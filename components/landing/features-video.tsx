"use client";

import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { Comparison, ComparisonHandle, ComparisonItem } from "@/components/ui/kibo-ui/comparison";
import { OSS_URL_HOST } from "@/lib/constants";
import { AspectRatio } from "../ui/aspect-ratio";

type Feature = {
	title: string;
	description: string;
	media: string;
};

export default function FeaturesVideoComponent({
	ctaText,
	ctaTextDisplay,
	ctaUrl,
	ctaClassName,
	features,
}: {
	ctaText: string;
	ctaTextDisplay?: boolean;
	ctaUrl: string;
	ctaClassName?: string;
	features: Feature[];
}) {
	return (
		<>
			{features.map((feature, index) => (
				<div key={index} className="container flex flex-col items-center gap-16 px-6 py-20">
					<div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 lg:gap-16">
						<div className={cn("block w-full", index % 2 === 0 ? "" : "md:order-last")}>
							<div className="relative h-full w-full">
								{/* <img
										src={feature.image}
										alt={feature.imageAlt ?? feature.title}
										className="h-full w-full rounded-lg object-cover"
										loading="lazy"
									/> */}
								<video
									src={feature.media}
									muted
									className="h-full w-full rounded-lg border"
									onContextMenu={(e) => e.preventDefault()}
									onDragStart={(e) => e.preventDefault()}
									onMouseEnter={(e) => {
										(e.target as HTMLVideoElement).play();
										(e.target as HTMLVideoElement).controls = true;
									}}
									onMouseLeave={(e) => {
										(e.target as HTMLVideoElement).pause();
										(e.target as HTMLVideoElement).controls = false;
									}}
									controlsList="nodownload noplaybackrate"
									disablePictureInPicture
									disableRemotePlayback
									preload="metadata"
								/>
							</div>
						</div>
						<div className="flex h-full flex-col items-start justify-between gap-4">
							<div className="flex flex-col gap-2">
								<h2 className="text-[32px] font-semibold text-balance">{feature.title}</h2>
								<p className="text-muted-foreground text-base">{feature.description}</p>
							</div>
							{/* <NoPrefetchLink
								href={ctaUrl}
								className={cn(buttonVariants({ size: "lg" }), "after:content-(--content)")}
								style={{ "--content": `'Try HiDream I1 Now'` } as React.CSSProperties}
							></NoPrefetchLink> */}
							{ctaTextDisplay ? (
								<NoPrefetchLink href={ctaUrl} className={cn(buttonVariants({ size: "lg", variant: "default" }), ctaClassName)}>
									{ctaText}
								</NoPrefetchLink>
							) : (
								<NoPrefetchLink
									href={ctaUrl}
									className={cn(buttonVariants({ size: "lg", variant: "default" }), "after:content-(--content)", ctaClassName)}
									style={{ "--content": `'${ctaText}'` } as React.CSSProperties}
								></NoPrefetchLink>
							)}
						</div>
					</div>
				</div>
			))}
		</>
	);
}
