import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";

type Feature = {
	title: string;
	description: string;
	image: string;
	imageAlt?: string;
};

export default function FeaturesComponent({ ctaText, ctaUrl, features }: { ctaText: string; ctaUrl: string; features: Feature[] }) {
	return (
		<>
			{features.map((feature, index) => (
				<div key={index} className="container flex flex-col items-center gap-16 px-6 py-20">
					<div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 lg:gap-16">
						<div className={cn("block w-full", index % 2 === 0 ? "" : "md:order-last")}>
							<AspectRatio ratio={3 / 2}>
								<img
									src={feature.image}
									alt={feature.imageAlt ?? feature.title}
									className="h-full w-full rounded-lg object-cover"
									loading="lazy"
								/>
							</AspectRatio>
						</div>
						<div className="flex h-full flex-col items-start justify-between gap-4">
							<div className="flex flex-col gap-2">
								<h2 className="text-balance text-[32px] font-semibold">{feature.title}</h2>
								<p className="text-base text-muted-foreground">{feature.description}</p>
							</div>
							<NoPrefetchLink
								href={ctaUrl}
								className={cn(buttonVariants({ size: "lg" }), "bg-teal-500 after:content-(--content) hover:bg-teal-600")}
								style={{ "--content": `'${ctaText}'` } as React.CSSProperties}
							></NoPrefetchLink>
						</div>
					</div>
				</div>
			))}
		</>
	);
}
