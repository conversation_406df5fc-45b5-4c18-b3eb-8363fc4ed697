import { cn } from "@/lib/utils";

export const Logo = ({ className }: { className?: string }) => {
	return (
		// <link rel="icon" href="/favicon.ico" sizes="any" />
		<img className={cn("size-7 rounded", className)} src="/favicon.ico" alt="dreampik.art" />
		// <svg xmlns="http://www.w3.org/2000/svg" className={cn("size-8 w-8", className)} viewBox="0 0 1024 1024">
		// 	<defs>
		// 		<linearGradient id="gradient_0" gradientUnits="userSpaceOnUse" x1="424.59216" y1="280.77103" x2="77.944115" y2="227.18886">
		// 			<stop offset="0" stopColor="#2F3BE5" />
		// 			<stop offset="1" stopColor="#3BCBFD" />
		// 		</linearGradient>
		// 	</defs>
		// 	<path
		// 		fill="url(#gradient_0)"
		// 		transform="scale(2 2)"
		// 		d="M262.682 446.043L262.107 446.202C251.09 449.3 244.448 443.783 235.264 438.732L104.553 368.35C87.0507 358.712 87.3035 343.98 87.3169 326.651L87.3142 196.393C87.3017 169.632 81.663 154.933 108.133 140.324L188.25 96.5147C203.833 87.9723 219.141 78.9134 234.845 70.5784C255.375 59.6818 255.99 59.3626 274.989 69.5524L335.165 101.782C344 106.605 353.025 111.056 361.731 116.127C370.176 120.009 378.158 125.016 386.358 129.383L401.96 137.588C426.691 150.572 424.995 158.449 425.003 185.718L425.005 258.187C425.005 263.161 426.035 278.416 424.238 282.489C420.844 290.185 409.878 289.863 407.591 281.748C406.563 278.103 407.16 265.346 407.16 260.739L407.152 192.503C407.156 180.715 411.379 162.884 400.714 156.651C386.496 148.341 371.639 140.905 357.112 133.111C351.624 130.167 345.627 127.492 340.423 124.104C335.796 121.862 331.399 119.153 326.849 116.761L276.777 89.8664C252.724 77.1265 260.351 76.0639 236.318 89.3487L122.399 151.327C114.178 155.831 105.065 158.994 104.611 169.913C104.238 178.858 104.634 187.97 104.633 196.934L104.623 311.188C104.623 320.653 104.249 330.267 104.628 339.718C105.041 350.013 113.742 353.279 121.477 357.44L238.707 420.739C253.222 428.422 252.291 432.778 268.739 423.661C275.378 419.98 282.056 415.779 288.93 412.601C295.129 408.371 303.552 413.893 301.897 421.443C300.441 428.087 285.623 433.542 280.051 436.554C274.309 439.66 268.593 443.308 262.682 446.043Z"
		// 	/>
		// 	<path
		// 		fill="#3869F0"
		// 		transform="scale(2 2)"
		// 		d="M288.93 412.601C295.129 408.371 303.552 413.893 301.897 421.443C300.441 428.087 285.623 433.542 280.051 436.554C274.309 439.66 268.593 443.308 262.682 446.043C257.136 441.895 282.854 429.741 287.358 420.317C288.55 417.822 288.058 415.166 288.93 412.601Z"
		// 	/>
		// 	<defs>
		// 		<linearGradient id="gradient_1" gradientUnits="userSpaceOnUse" x1="366.00732" y1="284.86978" x2="265.51791" y2="272.0452">
		// 			<stop offset="0" stopColor="#3268EE" />
		// 			<stop offset="1" stopColor="#3391F6" />
		// 		</linearGradient>
		// 	</defs>
		// 	<path
		// 		fill="url(#gradient_1)"
		// 		transform="scale(2 2)"
		// 		d="M343.609 168.095C354.939 167.115 353.465 178.078 353.457 185.659L353.476 374.822C353.478 380.087 355.041 389.759 351.027 393.909C346.122 398.98 337.356 396.309 336.315 389.179C335.879 386.198 336.063 383.033 336.061 380.021L336.067 315.355L336.067 191.721C331.223 193.544 324.055 198.037 319.356 200.733C312.401 204.725 290.211 218.335 283.081 220.152C276.292 221.882 270.429 212.965 274.079 207.164C276.847 202.764 292.256 195.94 297.25 193.154L324.118 178.273C328.793 175.674 339.172 169.019 343.609 168.095Z"
		// 	/>
		// </svg>
	);
};

export const LogoStroke = ({ className }: { className?: string }) => {
	return (
		<svg className={cn("size-7 w-7", className)} viewBox="0 0 71 25" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M61.25 1.625L70.75 1.5625C70.75 4.77083 70.25 7.79167 69.25 10.625C68.2917 13.4583 66.8958 15.9583 65.0625 18.125C63.2708 20.25 61.125 21.9375 58.625 23.1875C56.1667 24.3958 53.4583 25 50.5 25C46.875 25 43.6667 24.2708 40.875 22.8125C38.125 21.3542 35.125 19.2083 31.875 16.375C29.75 14.4167 27.7917 12.8958 26 11.8125C24.2083 10.7292 22.2708 10.1875 20.1875 10.1875C18.0625 10.1875 16.25 10.7083 14.75 11.75C13.25 12.75 12.0833 14.1875 11.25 16.0625C10.4583 17.9375 10.0625 20.1875 10.0625 22.8125L0 22.9375C0 19.6875 0.479167 16.6667 1.4375 13.875C2.4375 11.0833 3.83333 8.64583 5.625 6.5625C7.41667 4.47917 9.54167 2.875 12 1.75C14.5 0.583333 17.2292 0 20.1875 0C23.8542 0 27.1042 0.770833 29.9375 2.3125C32.8125 3.85417 35.7708 5.97917 38.8125 8.6875C41.1042 10.7708 43.1042 12.3333 44.8125 13.375C46.5625 14.375 48.4583 14.875 50.5 14.875C52.6667 14.875 54.5417 14.3125 56.125 13.1875C57.75 12.0625 59 10.5 59.875 8.5C60.7917 6.5 61.25 4.20833 61.25 1.625Z"
				fill="none"
				strokeWidth={0.5}
				stroke="currentColor"
			/>
		</svg>
	);
};
