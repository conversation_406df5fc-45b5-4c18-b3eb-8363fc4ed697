"use client";

import React, { useState } from "react";
import { ChevronDown, CreditCard, MenuIcon, Power, Icon, History, CoinsIcon } from "lucide-react";
import { coinsStack } from "@lucide/lab";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Logo } from "@/components/logo";
import { useSession } from "@/lib/auth-client";
import { WEBNAME } from "@/lib/constants";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import {
	NavigationMenu,
	NavigationMenuContent,
	NavigationMenuItem,
	NavigationMenuLink,
	NavigationMenuList,
	NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useUserStore } from "@/store/useUserStore";
import { MembershipID } from "@/@types/membership-type";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useSignOut } from "@/hooks/use-signout";
import { toast } from "sonner";
import { AuthError, IgnoreError } from "@/@types/error";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";
import { useRouter } from "nextjs-toploader/app";
import { BannerTop } from "../shared/banner-top";
import { Badge } from "../ui/badge";

interface MenuItem {
	name: string;
	href?: string;
	icon?: React.ReactNode;
	target?: string;
	hidden?: boolean;
	items?: {
		name: string;
		href: string;
		description?: string;
		new?: boolean;
	}[];
}

const menuItems: MenuItem[] = [
	{ name: "Al Image Generator", href: "/ai-image-generator" },
	{
		name: "Image AI",
		items: [
			{ name: "Flux", href: "/flux" },
			{
				name: "Flux Kontext",
				href: "/flux/flux-kontext",
				new: true,
			},
			{ name: "Imagen 4", href: "/imagen", new: true },
			{ name: "Ideogram 3", href: "/ideogram" },
			{ name: "Recraft 3", href: "/recraft" },
			{ name: "Seedream 3", href: "/ai-image-generator?model=seedream-3", new: true },
			{ name: "HiDream", href: "/hidream" },
			{ name: "Gemini Flash", href: "/gemini" },
		],
	},
	{ name: "Al Video Generator", href: "/ai-video-generator" },
	{
		name: "Video AI",
		items: [
			{ name: "Veo 3", href: "/veo/veo3", new: true },
			{
				name: "Kling 2.1",
				href: "/ai-video-generator?model=kling-2.1-master",
				new: true,
			},
			{ name: "Pixverse 4.5", href: "/ai-video-generator?model=pixverse-4.5" },
			{ name: "Hailuo 2", href: "/hailuo", new: true },
			{ name: "Seedance", href: "/ai-video-generator?model=seedance-1-lite", new: true },
			{ name: "LTX Video", href: "/ai-video-generator?model=ltx-video-13b-distilled" },
		],
	},
	// { name: "Blog", href: "/blog" },
	{ name: "Pricing", href: "/pricing" },
];

export const Header = () => {
	const router = useRouter();
	const { data: session } = useSession();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const { handleSignOut } = useSignOut();
	const { user, refreshUser } = useUserStore();

	const [showMobileMenu, setShowMobileMenu] = useState<boolean>(false);

	const [refreshingUserCredits, setRefreshingUserCredits] = useState<boolean>(false);
	const handleRefreshUserCredits = async () => {
		if (!session?.user) {
			return;
		}

		try {
			setRefreshingUserCredits(true);
			await refreshUser();
		} catch (error: any) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}

			if (error instanceof IgnoreError) {
				return;
			}
			toast.error("Network error");
		} finally {
			setRefreshingUserCredits(false);
		}
	};

	return (
		<header className="bg-background sticky top-0 z-20 w-full border-b transition-colors duration-300">
			<BannerTop />
			<div className="flex h-14 flex-wrap items-center justify-between px-6">
				<div className="flex flex-row items-center gap-8">
					<NoPrefetchLink href="/" className="flex items-center gap-2 rtl:space-x-reverse">
						<Logo className="rounded" />
						<span className="text-xl font-semibold">{WEBNAME}</span>
					</NoPrefetchLink>
					<div className="hidden rounded-lg font-normal md:flex-row lg:flex">
						<NavigationMenu viewport={false}>
							<NavigationMenuList className="space-x-0">
								{menuItems.map((route, index) => (
									<React.Fragment key={index}>
										{route.items ? (
											<NavigationMenuItem>
												<NavigationMenuTrigger className="bg-transparent px-3 font-[350] hover:bg-transparent focus:bg-transparent data-active:bg-transparent data-[state=open]:bg-transparent">
													{route.name}
												</NavigationMenuTrigger>
												<NavigationMenuContent>
													<div className="space-y-2">
														{/* <p className="text-sm font-medium">{route.name}</p> */}
														<div className="flex w-[240px] flex-col gap-0.5">
															{route.items.map((feature, index) => (
																<NavigationMenuLink key={index} asChild>
																	<NoPrefetchLink href={feature.href}>
																		<div className="flex flex-row items-center">
																			{feature.name}
																			{feature.new && (
																				<Badge
																					variant="secondary"
																					className="ml-1 rounded-full bg-green-500 px-1.5 py-0.5 text-[10px]"
																				>
																					New
																				</Badge>
																			)}
																		</div>
																		{feature.description && (
																			<div className="text-muted-foreground">{feature.description}</div>
																		)}
																	</NoPrefetchLink>
																</NavigationMenuLink>
															))}
														</div>
													</div>
												</NavigationMenuContent>
											</NavigationMenuItem>
										) : (
											<NavigationMenuItem>
												<NavigationMenuLink className={cn("hover:text-accent-foreground px-3 py-2 hover:bg-transparent")} asChild>
													<NoPrefetchLink href={route.href!} className="flex flex-row items-center font-[350]" target={route.target}>
														{route.name}
														{route.icon && <>{route.icon}</>}
													</NoPrefetchLink>
												</NavigationMenuLink>
											</NavigationMenuItem>
										)}
									</React.Fragment>
								))}
							</NavigationMenuList>
						</NavigationMenu>
					</div>
				</div>

				<div className="flex flex-row items-center gap-3">
					{session ? (
						<>
							<Button
								variant="secondary"
								size="sm"
								className="cursor-pointer bg-indigo-500 font-[380] hover:bg-indigo-500"
								onClick={() => setPlanBoxOpen(true)}
							>
								{/* <Icon iconNode={coinsStack} /> */}
								<CoinsIcon /> {(user?.creditOneTime ?? 0) + (user?.creditFree ?? 0)}
							</Button>
							{/* {user?.membershipId === MembershipID.Free && (
								<button className="relative flex items-center gap-2 text-xs" onClick={() => setPlanBoxOpen(true)}>
									<Crown className="size-3.5 fill-current text-yellow-500" />
									Upgrade
								</button>
							)} */}
							<DropdownMenu modal={false}>
								<DropdownMenuTrigger asChild className="cursor-pointer">
									<div className="flex shrink-0 flex-row items-center gap-2">
										<Avatar className="h-7 w-7">
											<AvatarImage src={session?.user.image!} alt="User Avatar" />
											<AvatarFallback>{session?.user.name}</AvatarFallback>
										</Avatar>
										<ChevronDown className="size-3.5 shrink-0 text-neutral-600 lg:block" />
									</div>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="w-[280px] p-0" align="end" forceMount>
									<div className="flex gap-5 p-5">
										<Avatar className="flex size-9 shrink-0 items-center gap-2 text-neutral-800">
											<AvatarImage src={session?.user.image!} alt="User Avatar" />
											<AvatarFallback>{session?.user.name}</AvatarFallback>
										</Avatar>
										<div className="flex min-w-0 flex-1 flex-col items-start">
											<p className="truncate text-sm font-semibold">{session?.user.name ?? WEBNAME}</p>
											<p className="text-muted-foreground mt-1 truncate text-xs">{session?.user.email ?? "--"}</p>
										</div>
									</div>
									{/* {user?.membershipId === MembershipID.Free && (
										<div className="px-[24px] pb-5">
											<Button size="sm" className="w-full bg-indigo-500 hover:bg-indigo-600" onClick={() => setPlanBoxOpen(true)}>
												Get a plan
											</Button>
										</div>
									)} */}

									<div className="flex flex-row items-center justify-between px-[24px] pb-4">
										<div className="flex flex-row items-center gap-1 text-sm font-[350]">
											<Icon iconNode={coinsStack} className="size-4 text-green-500" />{" "}
											{(user?.creditOneTime ?? 0) + (user?.creditFree ?? 0)} credits
											{/* <SubmitButton
												isSubmitting={refreshingUserCredits}
												variant="ghost"
												size="icon"
												className="h-6 w-6 hover:text-indigo-500"
												onClick={() => handleRefreshUserCredits()}
											>
												<RefreshCw className="size-4" />
											</SubmitButton> */}
										</div>
										<Button
											size="sm"
											variant="secondary"
											className="cursor-pointer bg-indigo-500 hover:bg-indigo-600"
											onClick={() => setPlanBoxOpen(true)}
										>
											Get more
										</Button>
									</div>
									<Separator />

									<NoPrefetchLink
										href="/my-creations"
										className="hover:bg-muted flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100"
									>
										<div className="flex flex-row items-center">
											<History className="mr-5 h-4 w-4 shrink-0" />
											My Creations
										</div>
									</NoPrefetchLink>
									<NoPrefetchLink
										href="/user/my-billing"
										className="hover:bg-muted flex h-11 w-full cursor-pointer flex-row items-center justify-between px-[24px] text-xs transition-all duration-100"
									>
										<div className="flex flex-row items-center">
											<CreditCard className="mr-5 h-4 w-4 shrink-0" />
											My Billing
										</div>
										{/* <p className="flex items-center gap-1 rounded bg-muted px-2 py-1 text-xs font-medium">{user?.membershipFormatted}</p> */}
									</NoPrefetchLink>
									{/* <NoPrefetchLink
										href={FEEDBACK_URL}
										target="_blank"
										className="flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100 hover:bg-muted"
									>
										<MessageCircle className="mr-5 h-4 w-4 shrink-0" />
										Got Feedback
									</NoPrefetchLink> */}

									<Separator />

									<button
										className="hover:bg-muted flex h-11 w-full cursor-pointer items-center px-[24px] text-xs transition-all duration-100"
										onClick={handleSignOut}
									>
										<Power className="mr-5 h-4 w-4 shrink-0" />
										Sign out
									</button>
								</DropdownMenuContent>
							</DropdownMenu>
						</>
					) : (
						<Button size="sm" onClick={() => setSignInBoxOpen(true)} className="cursor-pointer">
							Sign In
						</Button>
					)}
					<button className="-mr-2 flex cursor-pointer lg:hidden" onClick={() => setShowMobileMenu(true)}>
						<MenuIcon className="size-6" />
					</button>
				</div>
			</div>

			<Dialog open={showMobileMenu} onOpenChange={setShowMobileMenu}>
				<DialogContent className="h-[97vh] w-[95vw] max-w-full rounded bg-zinc-900">
					<DialogHeader>
						<DialogTitle className="text-start">
							<NoPrefetchLink href="/" className="flex items-center space-x-2">
								<Logo />
								<span className="text-lg font-semibold">{WEBNAME}</span>
							</NoPrefetchLink>
						</DialogTitle>
						<div className="h-full pt-3 text-start">
							{menuItems.map((route, index) => (
								<React.Fragment key={index}>
									{route.items ? (
										<div className="space-y-2">
											<Accordion type="single" collapsible>
												<AccordionItem value="item-1">
													<AccordionTrigger className="py-3 text-base font-normal hover:no-underline">{route.name}</AccordionTrigger>
													<AccordionContent className="space-y-2">
														{route.items.map((route, index) => (
															<div key={index} className="text-zinc-200">
																<NoPrefetchLink href={route.href} className="" onClick={() => setShowMobileMenu(false)}>
																	<p className="items-center">{route.name}</p>
																</NoPrefetchLink>
															</div>
														))}
													</AccordionContent>
												</AccordionItem>
											</Accordion>
										</div>
									) : (
										<div className="py-3">
											<NoPrefetchLink
												href={route.href!}
												className="font-normal"
												target={route.target}
												onClick={() => setShowMobileMenu(false)}
											>
												<p className="items-center">
													{route.name}
													{route.icon && <>{route.icon}</>}
												</p>
											</NoPrefetchLink>
										</div>
									)}
									<Separator className="" />
								</React.Fragment>
							))}
						</div>
						{/* <DialogFooter>
							{session ? (
								<NoPrefetchLink href={ROUTE_PATH_SIGN_IN} className={cn(buttonVariants(), "mx-auto")}>
									Dashboard
								</NoPrefetchLink>
							) : (
								<NoPrefetchLink href={ROUTE_PATH_SIGN_IN} className={cn(buttonVariants(), "mx-auto")}>
									Get Started
								</NoPrefetchLink>
							)}
						</DialogFooter> */}
					</DialogHeader>
				</DialogContent>
			</Dialog>
		</header>
	);
};
