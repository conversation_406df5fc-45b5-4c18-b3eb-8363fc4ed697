"use client";

import { useState } from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { Loader2, Check, X, Info } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { pricingOnetime, PricingOnetime } from "@/config/pricing";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AuthError, handleError } from "@/@types/error";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ofetch } from "ofetch";
import { CLICK_CHECKOUT } from "@/lib/umami-event-name";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { useRouter } from "nextjs-toploader/app";
import { HintPopover } from "@/components/ui/custom/hint";

export default function Plans({ hasFree = true }: { hasFree?: boolean }) {
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { planBoxOpen, setPlanBoxOpen } = usePlanBoxOpenStore();
	const router = useRouter();

	const [isPurchasing, setIsPurchasing] = useState(false);
	const [purchaseProductId, setPurchaseProductId] = useState<string | null>(null);

	const purchase = async (productId: string) => {
		console.log("New checkout");
		try {
			setPurchaseProductId(productId);
			setIsPurchasing(true);
			const { status, message, url } = await ofetch("/api/payment/checkout", {
				method: "POST",
				body: { productId, type: "onetime" },
			});
			handleError(status, message);
			if (url) {
				router.push(url);
				// window.open(url, "_blank");
			}
		} catch (error) {
			console.error(error);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
			} else {
				toast.error("Error creating a checkout.");
			}
			setIsPurchasing(false);
		}
		// finally {
		// 	setIsPurchasing(false);
		// }
	};

	return (
		<div className="flex w-full flex-col gap-8">
			<div
				className={cn(
					"mx-auto grid w-full max-w-3xl grid-cols-1 justify-center gap-3 rounded-lg text-start md:grid-cols-3 md:gap-0",
					hasFree ? "md:grid-cols-3" : "",
				)}
			>
				{pricingOnetime.map((pricing: PricingOnetime, index: number) => (
					<Card
						key={index}
						className={cn(
							"relative mx-auto w-full max-w-full gap-2 rounded-lg border bg-zinc-900/80 shadow-none hover:bg-zinc-800/80",
							pricing.badge && "z-10 rounded-lg border border-indigo-500/80 bg-[#272842] hover:bg-[#2f2f55] md:scale-105 md:rounded-xl",
						)}
					>
						{/* {pricing.badge && (
							<Badge
								variant="default"
								className="absolute left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-linear-to-r from-indigo-600 to-indigo-400 font-normal hover:bg-primary/80"
							>
								{pricing.badge}
							</Badge>
						)} */}
						<CardHeader>
							<CardTitle className="flex flex-row items-center gap-1 text-xl font-medium">
								{pricing.credits} credits{" "}
								{pricing.tip && (
									<HintPopover label={pricing.tip}>
										<Info className="text-muted-foreground h-4 w-4 cursor-pointer" />
									</HintPopover>
								)}
							</CardTitle>
						</CardHeader>

						<CardContent className="flex flex-col gap-8">
							<div className="flex flex-wrap items-end">
								<span className="text-2xl">
									{pricing.currency.symbol}
									{pricing.price}
								</span>
							</div>

							{pricing.free ? (
								<NoPrefetchLink
									href="/ai-image-generator"
									className={cn(buttonVariants({ variant: "outline" }), "w-full")}
									onClick={() => {
										if (planBoxOpen) {
											setPlanBoxOpen(false);
										}
									}}
								>
									<p className="flex flex-row items-center space-x-1">
										<span>Get Started</span>
									</p>
								</NoPrefetchLink>
							) : (
								<Button
									{...{
										variant: pricing.badge ? "secondary" : "default",
										disabled: isPurchasing && purchaseProductId === pricing.productId,
									}}
									className={cn("w-full cursor-pointer", pricing.badge && "bg-indigo-500 hover:bg-indigo-500/80")}
									onClick={() => {
										purchase(pricing.productId);
									}}
									data-umami-event={CLICK_CHECKOUT}
								>
									<p className="flex flex-row items-center space-x-1">
										<span>Get Started</span>
										{isPurchasing && purchaseProductId === pricing.productId && <Loader2 className="h-4 w-4 animate-spin" />}
									</p>
								</Button>
							)}

							<div className="flex flex-col gap-4">
								<div className="flex flex-col gap-3 text-sm font-[350] text-zinc-300">
									{pricing.features?.map((feature: any, index: any) => (
										<p key={index} className="flex items-start gap-3">
											<Check className="mt-0.5 h-4 w-4 shrink-0 text-green-700" />
											<span className="flex items-center gap-1">
												{feature.description}
												{feature.tips && (
													<TooltipProvider delayDuration={100}>
														<Tooltip>
															<TooltipTrigger>
																<Info className="text-muted-foreground h-4 w-4" />
															</TooltipTrigger>
															<TooltipContent className="bg-black">
																<p style={{ whiteSpace: "pre-wrap" }}>{feature.tips}</p>
															</TooltipContent>
														</Tooltip>
													</TooltipProvider>
												)}
											</span>
										</p>
									))}

									{pricing.unFeatures?.map((feature: any, index: any) => (
										<p key={index} className="flex items-start gap-3">
											<X className="mt-0.5 h-4 w-4 shrink-0 text-red-700" />
											<span className="">{feature}</span>
										</p>
									))}
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}
