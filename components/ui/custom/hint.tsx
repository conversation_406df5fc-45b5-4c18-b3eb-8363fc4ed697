import type { PropsWithChildren } from "react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/originui/tooltip";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface HintProps {
	label: string;
	side?: "top" | "bottom" | "left" | "right";
	align?: "start" | "center" | "end";
	sideOffset?: number;
	alignOffset?: number;
	className?: string;
}

export const Hint = ({ label, children, align, side, alignOffset, sideOffset, className }: PropsWithChildren<HintProps>) => {
	return (
		<TooltipProvider>
			<Tooltip delayDuration={100}>
				<TooltipTrigger asChild>{children}</TooltipTrigger>

				<TooltipContent className="dark py-3">
					<div className="space-y-1">
						<p className="text-[13px]">{label}</p>
					</div>
				</TooltipContent>
			</Tooltip>
		</TooltipProvider>
	);
};

interface HintPopoverProps {
	title?: string;
	label: string;
	className?: string;
}

export const HintPopover = ({ children, title, label, className }: PropsWithChildren<HintPopoverProps>) => {
	return (
		<Popover>
			<PopoverTrigger asChild>{children}</PopoverTrigger>
			<PopoverContent className="max-w-[280px] py-3 shadow-none" side="top">
				<div className="space-y-3">
					<div className="space-y-1">
						{title && <p className="text-[13px] font-medium">Popover with button</p>}
						<p className="text-muted-foreground text-xs">{label}</p>
					</div>
				</div>
			</PopoverContent>
		</Popover>
	);
};
