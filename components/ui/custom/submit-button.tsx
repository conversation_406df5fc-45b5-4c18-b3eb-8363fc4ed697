import { Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface SubmitButtonProps extends Omit<React.ComponentProps<typeof Button>, "button"> {
	children?: React.ReactNode;
	isSubmitting: boolean;
}

export function SubmitButton({ children, isSubmitting, ...props }: SubmitButtonProps) {
	return (
		<Button disabled={isSubmitting} {...props}>
			{isSubmitting ? <Loader2 className="h-4 w-4 animate-spin" /> : children}
		</Button>
	);
}
