import { LucideIcon, Type, FileImage, ImageUpscale, Images, Video, FileVideo, ImageIcon, ChevronsUpIcon } from "lucide-react";

export type AppModelType = {
	name: string;
	url: string;
	new?: boolean;
};
export const imageModels: AppModelType[] = [
	{
		name: "Flux",
		url: "/flux",
	},
	{
		name: "Flux Dev",
		url: "/flux",
	},
	{
		name: "Flux 1.1 Pro",
		url: "/flux",
	},
	{
		name: "Flux 1.1 Pro Ultra",
		url: "/flux",
	},
	{
		name: "Flux Kontext",
		url: "/flux/flux-kontext",
		new: true,
	},
	{
		name: "Imagen 4",
		url: "/imagen",
		new: true,
	},
	{
		name: "Ideogram 3",
		url: "/ideogram",
	},
	{
		name: "Recraft 3",
		url: "/recraft",
	},
	{ name: "Seedream 3", url: "/ai-image-generator?model=seedream-3", new: true },
	{
		name: "HiDream",
		url: "/hidream",
	},
	{
		name: "Gemini Flash",
		url: "/gemini",
	},
];
export const videoModels: AppModelType[] = [
	{
		name: "Veo 3",
		url: "/veo",
		new: true,
	},
	{
		name: "Kling 2.1",
		url: "/ai-video-generator?model=kling-2.1-master",
		new: true,
	},
	{ name: "Pixverse 4.5", url: "/ai-video-generator?model=pixverse-4.5" },
	{ name: "Hailuo 2", url: "/hailuo", new: true },
	{ name: "Seedance", url: "/ai-video-generator?model=seedance-1-lite", new: true },
	{ name: "LTX Video", url: "/ai-video-generator?model=ltx-video-13b-distilled" },
];

export type AppToolsType = {
	title: string;
	tools: {
		title: string;
		description?: string;
		url: string;
		icon: LucideIcon;
	}[];
};
export const imageAITools: AppToolsType = {
	title: "Image AI",
	tools: [
		{
			title: "Image Generator",
			description: "Text to image generation",
			url: "ai-image-generator",
			icon: ImageIcon,
		},
		{
			title: "Image to Image",
			description: "Image to image generation",
			url: "/image-to-image",
			icon: Images,
		},
		{
			title: "Image Editor",
			description: "Edit images with AI",
			url: "/flux/flux-kontext",
			icon: FileImage,
		},
		{
			title: "Image Upscaler",
			description: "Increase image resolution",
			url: "/image-upscaler",
			icon: ChevronsUpIcon,
		},
	],
};
export const videoAITools: AppToolsType = {
	title: "Video AI",
	tools: [
		{
			title: "Image to Video",
			description: "Image to video generation",
			url: "/image-to-video",
			icon: FileVideo,
		},
		{
			title: "Text to Video",
			description: "Text to video generation",
			url: "/ai-video-generator",
			icon: Video,
		},
	],
};
