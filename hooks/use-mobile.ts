import * as React from "react";

export function useIsMobile(mobileBreakpoint = 1024) {
	const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined);

	React.useEffect(() => {
		const mql = window.matchMedia(`(max-width: ${mobileBreakpoint - 1}px)`);
		const onChange = () => {
			setIsMobile(window.innerWidth < mobileBreakpoint);
		};
		mql.addEventListener("change", onChange);
		setIsMobile(window.innerWidth < mobileBreakpoint);
		return () => mql.removeEventListener("change", onChange);
	}, []);

	return !!isMobile;
}
