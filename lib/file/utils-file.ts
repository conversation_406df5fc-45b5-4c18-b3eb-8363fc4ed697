import { ofetch } from "ofetch";
import { WEBNAME } from "../constants";
import { getNanoId } from "../utils";

/**
 * Get file extension
 */
export const getFileExtension = (fileUrl: string) => {
	const extension = fileUrl.split(".").pop()?.split("?")[0];
	if (!extension) {
		throw new Error("Invalid file URL");
	}
	return extension;
};
/**
 * 根据 Video URL 后缀名获取 Content-Type。
 *
 * @param videoUrl 图片 URL。
 * @returns  对应的 Content-Type 字符串，如果无法识别则返回 null。
 */
export function getContentTypeFromVideoUrl(videoUrl: string): string | null {
	if (!videoUrl) {
		return null; // 或者抛出错误，根据你的需求
	}

	const urlLower = videoUrl.toLowerCase();

	if (urlLower.endsWith(".mp4")) {
		return "video/mp4";
	} else if (urlLower.endsWith(".webm")) {
		return "video/webm";
	} else if (urlLower.endsWith(".ogg")) {
		return "video/ogg";
	} else if (urlLower.endsWith(".mov")) {
		return "video/quicktime";
	} else if (urlLower.endsWith(".avi")) {
		return "video/x-msvideo";
	} else if (urlLower.endsWith(".wmv")) {
		return "video/x-ms-wmv";
	} else if (urlLower.endsWith(".flv")) {
		return "video/x-flv";
	} else if (urlLower.endsWith(".mkv")) {
		return "video/x-matroska";
	} else if (urlLower.endsWith(".m4a")) {
		return "video/mp4";
	} else {
		return null; // 无法识别的后缀名
	}
}

/**
 * 根据图片 URL 后缀名获取 Content-Type。
 *
 * @param imageUrl 图片 URL。
 * @returns  对应的 Content-Type 字符串，如果无法识别则返回 null。
 */
export function getContentTypeFromImageUrl(imageUrl: string): string | null {
	if (!imageUrl) {
		return null; // 或者抛出错误，根据你的需求
	}

	const urlLower = imageUrl.toLowerCase();

	if (urlLower.endsWith(".jpg") || urlLower.endsWith(".jpeg")) {
		return "image/jpeg";
	} else if (urlLower.endsWith(".png")) {
		return "image/png";
	} else if (urlLower.endsWith(".gif")) {
		return "image/gif";
	} else if (urlLower.endsWith(".bmp")) {
		return "image/bmp";
	} else if (urlLower.endsWith(".webp")) {
		return "image/webp";
	} else if (urlLower.endsWith(".svg")) {
		return "image/svg+xml";
	} else if (urlLower.endsWith(".tiff") || urlLower.endsWith(".tif")) {
		return "image/tiff";
	} else if (urlLower.endsWith(".ico")) {
		return "image/x-icon";
	} else {
		return null; // 无法识别的后缀名
	}
}

export const imageUrlToBase64 = async (
	url: string,
	options?: {
		noCache?: boolean;
		hasWatermark?: boolean;
	},
): Promise<string> => {
	const blob = await ofetch(url, {
		headers: options?.noCache
			? {
					"Cache-Control": "no-cache", // 如果是r2且刚上传的资源则需要该配置
				}
			: undefined,
		responseType: "blob",
	});
	const base64 = await fileToBase64(blob);

	if (!options?.hasWatermark) return base64;

	return addWatermarkToBase64(base64);
};

export const fileToBase64 = (file: Blob): Promise<string> => {
	return new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.onload = () => {
			if (typeof reader.result === "string") {
				resolve(reader.result);
			} else {
				reject(new Error("Failed to convert file to base64"));
			}
		};
		reader.onerror = () => reject(reader.error);
		reader.readAsDataURL(file);
	});
};

// 添加水印到base64图片
export const addWatermarkToBase64 = (base64: string): Promise<string> => {
	const imageWatermark = "/favicon.ico";
	const watermarkText = `${WEBNAME}.art`;
	return new Promise((resolve) => {
		const img = new Image();
		img.src = base64;
		img.onload = () => {
			const canvas = document.createElement("canvas");
			const ctx = canvas.getContext("2d", { alpha: true, willReadFrequently: true });

			// 使用2倍分辨率来提高渲染质量
			const scale = 2;
			canvas.width = img.width;
			canvas.height = img.height;

			// 启用抗锯齿
			if (ctx) {
				ctx.imageSmoothingEnabled = true;
				ctx.imageSmoothingQuality = "high";
			}

			// 绘制原图
			ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);

			if (ctx) {
				const watermarkImg = new Image();
				watermarkImg.src = imageWatermark;
				watermarkImg.onload = () => {
					// 设置水印尺寸
					const watermarkWidth = 48;
					const watermarkHeight = (watermarkImg.height / watermarkImg.width) * watermarkWidth;

					// 设置文字样式，提前测量宽度
					ctx.font = "bold 36px 'Arial', sans-serif";
					ctx.textBaseline = "middle";
					ctx.textAlign = "left";
					const textMetrics = ctx.measureText(watermarkText);
					const textWidth = textMetrics.width;
					const padding = 16; // 图片和文字之间的间距

					const totalWidth = watermarkWidth + padding + textWidth;
					const centerX = img.width / 2;
					// 放置在底部，上移30像素
					const bottomY = img.height - 30;

					// 计算起始x坐标，使整体居中
					const startX = centerX - totalWidth / 2;

					// 绘制文字阴影效果
					ctx.shadowColor = "rgba(0, 0, 0, 0.7)";
					ctx.shadowBlur = 3;
					ctx.shadowOffsetX = 1;
					ctx.shadowOffsetY = 1;

					// 绘制文字描边效果
					ctx.lineWidth = 2;
					ctx.strokeStyle = "rgba(0, 0, 0, 0.8)";
					ctx.strokeText(watermarkText, startX + watermarkWidth + padding, bottomY);

					// 绘制图片水印
					ctx.shadowColor = "transparent"; // 关闭阴影效果
					ctx.drawImage(watermarkImg, startX, bottomY - watermarkHeight / 2, watermarkWidth, watermarkHeight);

					// 绘制文字水印
					ctx.fillStyle = "white";
					ctx.fillText(watermarkText, startX + watermarkWidth + padding, bottomY);

					resolve(canvas.toDataURL());
				};
				watermarkImg.onerror = () => {
					addTextWatermark(ctx);
				};

				function addTextWatermark(ctx: CanvasRenderingContext2D) {
					// 文字水印放在底部
					ctx.font = "bold 36px 'Arial', sans-serif";
					ctx.textAlign = "center";
					ctx.textBaseline = "middle";

					// 添加阴影效果
					ctx.shadowColor = "rgba(0, 0, 0, 0.7)";
					ctx.shadowBlur = 3;
					ctx.shadowOffsetX = 1;
					ctx.shadowOffsetY = 1;

					// 添加描边确保文字可见
					ctx.lineWidth = 2;
					ctx.strokeStyle = "rgba(0, 0, 0, 0.8)";
					ctx.strokeText(watermarkText, img.width / 2, img.height - 30);

					// 填充文字
					ctx.fillStyle = "white";
					ctx.fillText(watermarkText, img.width / 2, img.height - 30);

					resolve(canvas.toDataURL());
				}
			} else {
				resolve(base64);
			}
		};
		img.onerror = () => resolve(base64);
	});
};

/**
 * Download image from url
 */
export const downloadImageFromUrl = async (imageUrl?: string | null, fileName?: string) => {
	if (!imageUrl) return;
	if (!fileName) fileName = `${getNanoId(6)}`;
	try {
		// const response = await fetch(imageUrl);
		// if (!response.ok) {
		// 	throw new Error(`Download image failed: ${response.status} ${response.statusText}`);
		// }
		// const blob = await response.blob();

		const blob = await ofetch(imageUrl, {
			headers: {
				"Cache-Control": "no-cache",
			},
			responseType: "blob",
		});
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");

		// 从原始URL中提取文件扩展名
		const extension = getFileExtension(imageUrl);

		a.href = url;
		a.download = `${fileName}.${extension}`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	} catch (error) {
		console.error("Download image failed:", error);
		throw error;
	}
};

/**
 * Download image from base64
 */
export const downloadImageFromBase64 = async (base64Data: string, fileName?: string) => {
	if (!base64Data) return;
	if (!fileName) fileName = `${getNanoId(6)}`;

	try {
		// Extract the mime type and base64 content
		const [header, content] = base64Data.split(",");
		const mimeType = header.match(/data:(.*?);/)?.[1] || "image/png";
		const extension = mimeType.split("/")[1];

		// Convert base64 to blob
		const byteCharacters = atob(content);
		const byteArrays = new Uint8Array(byteCharacters.length);

		for (let i = 0; i < byteCharacters.length; i++) {
			byteArrays[i] = byteCharacters.charCodeAt(i);
		}

		const blob = new Blob([byteArrays], { type: mimeType });
		const url = URL.createObjectURL(blob);

		// Create and trigger download
		const a = document.createElement("a");
		a.href = url;
		a.download = `${fileName}.${extension}`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	} catch (error) {
		console.error("Download image failed:", error);
		throw error;
	}
};
export const downloadImageFromBase64WithWatermark = async (base64Data: string, fileName?: string) => {
	const base64 = await addWatermarkToBase64(base64Data);
	await downloadImageFromBase64(base64);
};
