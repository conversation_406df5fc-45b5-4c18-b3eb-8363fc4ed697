import { ParamsError } from "@/@types/error";
import { OSS_URL_HOST } from "./constants";

export type ImageModel = {
	name: string;
	id: string; // for api, db
	model: string; // for image generatiton third platform api
	modelStyle?: {
		id: string;
		name: string;
	}[];
	credits: number;
	logo?: string;
	textToImage: boolean;
	imageToImage: boolean;
	noAspectRatio?: "all" | "image-to-image";
};

const FLUX_1_SCHNELL: ImageModel = {
	name: "Flux.1 Schnell",
	id: "flux-schnell",
	model: "flux-schnell",
	credits: 1,
	textToImage: true,
	imageToImage: true,
};
export const FLUX_1_FAST: ImageModel = {
	name: "Flux.1 Fast",
	id: "flux-1-fast",
	model: "flux-dev-ultra-fast",
	credits: 1,
	logo: `${OSS_URL_HOST}icon/model/flux-dark.webp`,
	textToImage: true,
	imageToImage: true,
};
export const FLUX_1_DEV: ImageModel = {
	name: "Flux.1 Dev",
	id: "flux-1-dev",
	model: "flux-dev",
	credits: 4,
	logo: `${OSS_URL_HOST}icon/model/flux-dark.webp`,
	textToImage: true,
	imageToImage: true,
};
export const FLUX_1_KONTEXT_DEV: ImageModel = {
	name: "Flux.1 Kontext Dev",
	id: "flux-1-kontext-dev",
	model: "flux-1-kontext-dev",
	credits: 5,
	logo: `${OSS_URL_HOST}icon/model/flux-dark.webp`,
	textToImage: false,
	imageToImage: true,
	noAspectRatio: "image-to-image",
};
export const FLUX_1_KONTEXT_PRO: ImageModel = {
	name: "Flux.1 Kontext Pro",
	id: "flux-1-kontext-pro",
	model: "flux-1-kontext-pro",
	credits: 8,
	logo: `${OSS_URL_HOST}icon/model/flux-dark.webp`,
	textToImage: true,
	imageToImage: true,
	noAspectRatio: "image-to-image",
};
export const FLUX_1_KONTEXT_MAX: ImageModel = {
	name: "Flux.1 Kontext Max",
	id: "flux-1-kontext-max",
	model: "flux-1-kontext-max",
	credits: 16,
	logo: `${OSS_URL_HOST}icon/model/flux-dark.webp`,
	textToImage: true,
	imageToImage: true,
	noAspectRatio: "image-to-image",
};
export const FLUX_1_1_PRO: ImageModel = {
	name: "Flux1.1 Pro",
	id: "flux-1.1-pro",
	model: "flux-1.1-pro",
	credits: 8,
	logo: `${OSS_URL_HOST}icon/model/flux-dark.webp`,
	textToImage: true,
	imageToImage: true,
};
export const FLUX_1_1_PRO_ULTRA: ImageModel = {
	name: "Flux1.1 Pro Ultra",
	id: "flux-1.1-pro-ultra",
	model: "flux-1.1-pro-ultra",
	credits: 12,
	logo: `${OSS_URL_HOST}icon/model/flux-dark.webp`,
	textToImage: true,
	imageToImage: true,
};
export const IMAGEN_3_FAST: ImageModel = {
	name: "Imagen 3",
	id: "imagen-3-fast",
	model: "imagen3/fast",
	credits: 5,
	logo: `${OSS_URL_HOST}icon/model/google-color.webp`,
	textToImage: true,
	imageToImage: false,
};
export const IMAGEN_4_PREVIEW: ImageModel = {
	name: "Imagen 4",
	id: "imagen-4-preview",
	model: "imagen4/preview",
	credits: 8,
	logo: `${OSS_URL_HOST}icon/model/google-color.webp`,
	textToImage: true,
	imageToImage: false,
};
export const IMAGEN_4_PREVIEW_FAST: ImageModel = {
	name: "Imagen 4 Fast",
	id: "imagen-4-preview-fast",
	model: "imagen4/preview/fast",
	credits: 4,
	logo: `${OSS_URL_HOST}icon/model/google-color.webp`,
	textToImage: true,
	imageToImage: false,
};
export const IMAGEN_4_PREVIEW_ULTRA: ImageModel = {
	name: "Imagen 4 Ultra",
	id: "imagen-4-preview-ultra",
	model: "imagen4/preview/ultra",
	credits: 15,
	logo: `${OSS_URL_HOST}icon/model/google-color.webp`,

	textToImage: true,
	imageToImage: false,
};
export const IDEOGRAM_3_TURBO: ImageModel = {
	name: "Ideogram 3 Turbo",
	id: "ideogram-3-turbo",
	model: "ideogram3",
	credits: 6,
	logo: `${OSS_URL_HOST}icon/model/ideogram-dark.webp`,
	textToImage: true,
	imageToImage: true,
};
export const RECRAFT_3: ImageModel = {
	name: "Recraft 3",
	id: "recraft-3",
	model: "recraft/v3",
	modelStyle: [
		{
			id: "realistic_image",
			name: "Realistic",
		},
		{
			id: "digital_illustration",
			name: "Illustration",
		},
	],
	credits: 8,
	logo: `${OSS_URL_HOST}icon/model/recraft-dark.webp`,
	textToImage: true,
	imageToImage: true,
	noAspectRatio: "image-to-image",
};
export const SEEDREAM_3: ImageModel = {
	name: "Seedream 3",
	id: "seedream-3",
	model: "seedream/v3",
	credits: 8,
	logo: `${OSS_URL_HOST}icon/model/bytedance-color.webp`,
	textToImage: true,
	imageToImage: false,
};
const imageModels: ImageModel[] = [
	FLUX_1_FAST,
	FLUX_1_DEV,
	FLUX_1_1_PRO,
	FLUX_1_1_PRO_ULTRA,
	FLUX_1_KONTEXT_DEV,
	FLUX_1_KONTEXT_PRO,
	FLUX_1_KONTEXT_MAX,
	IMAGEN_4_PREVIEW,
	IMAGEN_4_PREVIEW_FAST,
	IMAGEN_4_PREVIEW_ULTRA,
	IDEOGRAM_3_TURBO,
	RECRAFT_3,
	SEEDREAM_3,
];
export const textToImageModels: ImageModel[] = imageModels.filter((imageModel) => imageModel.textToImage);
export const imageToImageModels: ImageModel[] = imageModels.filter((imageModel) => imageModel.imageToImage);
export const pageImageModels: ImageModel[] = [
	FLUX_1_FAST,
	FLUX_1_DEV,
	FLUX_1_1_PRO,
	FLUX_1_KONTEXT_DEV,
	IMAGEN_4_PREVIEW_FAST,
	IDEOGRAM_3_TURBO,
	RECRAFT_3,
	SEEDREAM_3,
];

export const getImageModel = (id: string): ImageModel => {
	const imageModel = imageModels.find((imageModel) => imageModel.id === id);
	if (process.env.NODE_ENV === "development") {
		console.log("imageModel: ", imageModel);
	}
	if (!imageModel) {
		throw new ParamsError("Image model is not found.");
	}
	return imageModel;
};
export const getInitialImageModel = (id: string | null, imageToImage: boolean | undefined = undefined): ImageModel => {
	if (!id) return imageToImage ? imageToImageModels[0] : textToImageModels[0];

	if (imageToImage) {
		return imageToImageModels.find((model) => model.id === id) || imageToImageModels[0];
	}
	return textToImageModels.find((model) => model.id === id) || textToImageModels[0];
};

// Flux
export const fluxModels: ImageModel[] = [FLUX_1_SCHNELL, FLUX_1_DEV, FLUX_1_1_PRO, FLUX_1_1_PRO_ULTRA];
export const getFluxModel = (id: string): ImageModel => {
	const imageModel = fluxModels.find((fluxModel) => fluxModel.id === id)!;
	if (process.env.NODE_ENV === "development") {
		console.log("imageModel: ", imageModel);
	}
	if (!imageModel) {
		throw new ParamsError("Image model is not found.");
	}
	return imageModel;
};

// HiDream
export const HIDREAM_EI_FULL: ImageModel = {
	name: "HiDream E1 Full",
	id: "hidream-e1-full",
	model: "hidream-e1-full",
	credits: 6,
	textToImage: false,
	imageToImage: true,
};
export const hiDreamModels: ImageModel[] = [
	{
		name: "HiDream I1 Dev",
		id: "hidream-i1-dev",
		model: "hidream-i1-dev",
		credits: 4,
		textToImage: true,
		imageToImage: true,
	},
	{
		name: "HiDream I1 Full",
		id: "hidream-i1-full",
		model: "hidream-i1-full",
		credits: 10,
		textToImage: true,
		imageToImage: true,
	},
];
export const getHiDreamModel = (id: string): ImageModel => {
	const imageModel = hiDreamModels.find((imageModel) => imageModel.id === id);
	if (process.env.NODE_ENV === "development") {
		console.log("imageModel: ", imageModel);
	}
	if (!imageModel) {
		throw new ParamsError("Image model is not found.");
	}
	return imageModel;
};

// Gemini flash
export const geminiFlashCredits = 2;
export const GEMINI_FLASH: ImageModel = {
	name: "Gemini Flash",
	id: "gemini-flash",
	model: "gemini-flash",
	credits: 2,
	textToImage: false,
	imageToImage: true,
};

// Ideogram
export const ideogramCredits = (numImages: number, renderSpeed: string): number => {
	if (renderSpeed === "TURBO") {
		return numImages * 5;
	} else if (renderSpeed === "BALANCED") {
		return numImages * 10;
	} else if (renderSpeed === "QUALITY") {
		return numImages * 15;
	}
	return 20;
};

// Recraft
export const getRecraftCredits = (style: string): number => {
	if (style === "vector_illustration") return 16;
	return 8;
};
