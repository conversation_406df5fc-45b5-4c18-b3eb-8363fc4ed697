import { MembershipID, membershipMapping, MembershipPeriodMonth, MembershipPeriodType, MembershipPeriodYear, MembershipType } from "@/@types/membership-type";

// ======================order======================
// product dev
const ORDER_DEV_PRODUCT_ID_1 = "772883";
const ORDER_DEV_PRODUCT_ID_2 = "772884";
const ORDER_DEV_PRODUCT_ID_3 = "772885";
// product prod
const ORDER_PROD_PRODUCT_ID_1 = "772956";
const ORDER_PROD_PRODUCT_ID_2 = "772957";
const ORDER_PROD_PRODUCT_ID_3 = "772958";

export const ORDER_PRODUCT_ID_1 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_1 : ORDER_DEV_PRODUCT_ID_1;
export const ORDER_PRODUCT_ID_2 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_2 : ORDER_DEV_PRODUCT_ID_2;
export const ORDER_PRODUCT_ID_3 = process.env.NODE_ENV === "production" ? ORDER_PROD_PRODUCT_ID_3 : ORDER_DEV_PRODUCT_ID_3;

export interface OrderInfo {
	productId: string;
	membership: MembershipType;
	credits: number;
}

const orderVariants: OrderInfo[] = [
	{
		productId: ORDER_PRODUCT_ID_1,
		membership: membershipMapping[MembershipID.Growth],
		credits: 200,
	},
	{
		productId: ORDER_PRODUCT_ID_2,
		membership: membershipMapping[MembershipID.Growth],
		credits: 650,
	},

	{
		productId: ORDER_PRODUCT_ID_3,
		membership: membershipMapping[MembershipID.Growth],
		credits: 1600,
	},
];
export function getOrderProductInfo(productId: string | number): OrderInfo | undefined {
	if (typeof productId === "number") {
		productId = productId.toString();
	}
	return orderVariants.find((v) => v.productId === productId);
}

export enum OrderSource {
	Polar = "polar",
	Cream = "cream",
	Lmsqueezy = "lemonsqueezy",
}

// pending, paid, refunded, partially_refunded
export enum OrderStatus {
	Pending = "pending",
	Paid = "paid",
	Refunded = "refunded",
	PartiallyRefunded = "partially_refunded",
}
export function getOrderStatusName(status: OrderStatus): string {
	switch (status) {
		case OrderStatus.Pending:
			return "Pending";
		case OrderStatus.Paid:
			return "Paid";
		case OrderStatus.Refunded:
			return "Refunded";
		case OrderStatus.PartiallyRefunded:
			return "Partially Refunded";
		default:
			return "Unknown";
	}
}

// ======================Subscription Polar=====================
// incomplete, incomplete_expired, trialing, active, past_due, canceled, unpaid
export enum SubscriptionStatus {
	Incomplete = "incomplete",
	IncompleteExpired = "incomplete_expired",
	Trialing = "trialing",
	Active = "active",
	PastDue = "past_due",
	Canceled = "canceled",
	Unpaid = "unpaid",
}
export function getSubscriptionStatusName(status: SubscriptionStatus): string {
	switch (status) {
		case SubscriptionStatus.Incomplete:
			return "Incomplete";
		case SubscriptionStatus.IncompleteExpired:
			return "Incomplete Expired";
		case SubscriptionStatus.Trialing:
			return "Trialing";
		case SubscriptionStatus.Active:
			return "Active";
		case SubscriptionStatus.PastDue:
			return "Past Due";
		case SubscriptionStatus.Canceled:
			return "Canceled";
		case SubscriptionStatus.Unpaid:
			return "Unpaid";
		default:
			return "Unknown";
	}
}
// product dev
const SUBSCRIPTION_DEV_PRODUCT_ID_STARTER_MONTH = "71c6097a-d4b0-4146-a47c-c3db7844ef2a";
const SUBSCRIPTION_DEV_PRODUCT_ID_STARTER_YEAR = "75d766bf-3c62-471a-995f-25f13cdf21eb";
const SUBSCRIPTION_DEV_PRODUCT_ID_GROWTH_MONTH = "6e2b0631-555d-49fb-8697-5765a635f5cb";
const SUBSCRIPTION_DEV_PRODUCT_ID_GROWTH_YEAR = "c8667e0f-40fb-4d56-9252-3ffb23c1d9e1";
// product prod
const SUBSCRIPTION_PROD_PRODUCT_ID_STARTER_MONTH = "7ab112e0-6bd8-4746-a561-46042299c522";
const SUBSCRIPTION_PROD_PRODUCT_ID_STARTER_YEAR = "c23642ec-9c08-4ab9-a0f3-3513ee691608";
const SUBSCRIPTION_PROD_PRODUCT_ID_GROWTH_MONTH = "594d41f2-82b3-48b0-a699-05b23790246b";
const SUBSCRIPTION_PROD_PRODUCT_ID_GROWTH_YEAR = "c04b3656-3a7d-49e0-90a7-5995086b1d41";

export const SUBSCRIPTION_PRODUCT_ID_STARTER_MONTH =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_STARTER_MONTH : SUBSCRIPTION_DEV_PRODUCT_ID_STARTER_MONTH;
export const SUBSCRIPTION_PRODUCT_ID_STARTER_YEAR =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_STARTER_YEAR : SUBSCRIPTION_DEV_PRODUCT_ID_STARTER_YEAR;
export const SUBSCRIPTION_PRODUCT_ID_GROWTH_MONTH =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_GROWTH_MONTH : SUBSCRIPTION_DEV_PRODUCT_ID_GROWTH_MONTH;
export const SUBSCRIPTION_PRODUCT_ID_GROWTH_YEAR =
	process.env.NODE_ENV === "production" ? SUBSCRIPTION_PROD_PRODUCT_ID_GROWTH_YEAR : SUBSCRIPTION_DEV_PRODUCT_ID_GROWTH_YEAR;

export interface MembershipInfo {
	productId: string;
	membership: MembershipType;
	period: MembershipPeriodType;
}

const membershipVariants: MembershipInfo[] = [
	{
		productId: SUBSCRIPTION_PRODUCT_ID_STARTER_MONTH,
		membership: membershipMapping[MembershipID.Starter],
		period: MembershipPeriodMonth,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_STARTER_YEAR,
		membership: membershipMapping[MembershipID.Starter],
		period: MembershipPeriodYear,
	},

	{
		productId: SUBSCRIPTION_PRODUCT_ID_GROWTH_MONTH,
		membership: membershipMapping[MembershipID.Growth],
		period: MembershipPeriodMonth,
	},
	{
		productId: SUBSCRIPTION_PRODUCT_ID_GROWTH_YEAR,
		membership: membershipMapping[MembershipID.Growth],
		period: MembershipPeriodYear,
	},
];
export function getMembershipProductInfo(productId: string | number): MembershipInfo | undefined {
	if (typeof productId === "number") {
		productId = productId.toString();
	}
	return membershipVariants.find((v) => v.productId === productId);
}

// 限制用户会员降级，但这不是最好的方案，更好的方案为：**只能在下一个收费周期进行一个会员顶级更改**
// Starter月会员能更改升级成：Starter年会员、Growth月会员、Growth年会员
// Starter年会员能更改能升级成：Growth年会员
// Growth月会员能更改能升级成：Starter年会员、Growth年会员
// Growth年会员不能进行任何更改
export function canChangePlan(userProductId: string | number, targetProductId: string | number): boolean {
	const userMembershipInfo = getMembershipProductInfo(userProductId);
	if (!userMembershipInfo) return true;
	const targetMembershipInfo = getMembershipProductInfo(targetProductId);
	if (!targetMembershipInfo) return true;

	//Growth年会员不能进行任何更改
	if (userMembershipInfo.membership.id === MembershipID.Growth && userMembershipInfo.period.value === MembershipPeriodYear.value) {
		return false;
	}
	// Starter月会员能更改升级成：Starter年会员、Growth月会员、Growth年会员
	if (userMembershipInfo.membership.id === MembershipID.Starter && userMembershipInfo.period.value === MembershipPeriodMonth.value) {
		return true;
	}
	// Starter年会员只能更改能升级成：Growth年会员
	if (userMembershipInfo.membership.id === MembershipID.Starter && userMembershipInfo.period.value === MembershipPeriodYear.value) {
		if (targetMembershipInfo.membership.id === MembershipID.Growth && targetMembershipInfo.period.value === MembershipPeriodYear.value) {
			return true;
		}
		return false;
	}
	// Growth月会员只能更改能升级成：Starter年会员、Growth年会员
	if (userMembershipInfo.membership.id === MembershipID.Growth && userMembershipInfo.period.value === MembershipPeriodMonth.value) {
		if (targetMembershipInfo.membership.id === MembershipID.Growth && targetMembershipInfo.period.value === MembershipPeriodYear.value) {
			return true;
		}
		if (targetMembershipInfo.membership.id === MembershipID.Starter && targetMembershipInfo.period.value === MembershipPeriodYear.value) {
			return true;
		}
	}

	return false;
}
