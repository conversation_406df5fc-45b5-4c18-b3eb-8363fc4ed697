import { OSS_URL_HOST } from "./constants";

// photo style category
export enum StyleCategoryID {
	None = 0, // prompt only
	Reference = 1, //  reference image + prompt(optional)
	Cartoon = 10,
	ActionFigure = 11,
	Sketch = 12,
	Artist = 13,
	Art = 14,
}
export type StyleCategory = {
	id: number;
	name: string;
};

// photo style model
export enum PhotoStyleID {
	None = 0, // prompt only
	Reference = 1, //  reference image + prompt(optional)
	Cartoon = 1000,
	Ghibli = 1001,
	Lego = 1002,
	Anime = 1003,
	Pixel = 1004,
	Minecraft = 1005,
	Pixar = 1006, // Disney pixar style
	Barbie = 1007,
	SimpsonStyle = 1008,
	Chibi = 1009,
	SouthPark = 1010, // South park style
	Clay = 1011,
	Muppet = 1012, // Disney muppet style
	RickMorty = 1013, // Rick and morty style
	// RubberHoseAnimation = 1014,
	ActionFigure1 = 1100,
	ActionFigureBarbie = 1101,
	ActionFigureBusiness = 1103,
	ActionFigureSoccerPlayer = 1104,
	ActionFigureFunkoPop = 1005,
	// ActionFigureBasketballPlayer = 1105,
	// ActionFigureDoctor = 1106,
	// ActionFigureTeacher = 1107,
	// ActionFigurePoliceman = 1108,
	// ActionFigureFirefighter = 1109,
	// ActionFigureDesigner = 1105,
	// ActionFigureGardener = 1107,
	// ActionFigureRunner = 1108,
	// ActionFigureChef = 1109,
	// ActionFigureArtist = 1109,
	// ActionFigureEngineer = 1110,
	// ActionFigureSoldier = 1115,
	// ActionFigureScientist = 1116,
	Outline = 1200,
	LineArt = 1201,
}
export type StyleType = {
	id: number;
	name: string;
	prompt?: string;
	previewImageDemo?: string;
};
export type PhotoStyleType = {
	category: StyleCategory;
	style: StyleType[];
	promptPlaceholder?: string;
};
export const allPhotoEffects: StyleType[] = [
	{
		id: PhotoStyleID.Cartoon,
		name: "Cartoon",
		prompt: "Create a cartoon version of this image",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo_to_ghibli.webp`,
	},
	{
		id: PhotoStyleID.Ghibli,
		name: "Ghibli",
		prompt: "restyle image in studio Ghibli style keep all details",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo_to_ghibli.webp`,
	},
	{
		id: PhotoStyleID.Lego,
		name: "Lego",
		prompt: "restyle image in Lego style, detailed, 3D-like",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/lego-filter.webp`,
	},
	{
		id: PhotoStyleID.Anime,
		name: "Anime",
		prompt: "restyle image in Anime style keep all details",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo-to-anime.webp`,
	},
	{
		id: PhotoStyleID.Pixel,
		name: "Pixel",
		prompt: "Transform the image into polished pixel art style",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/lego-filter.webp`,
	},
	{
		id: PhotoStyleID.Minecraft,
		name: "Minecraft",
		prompt: "Transform the image into minecraft style",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/lego-filter.webp`,
	},
	{
		id: PhotoStyleID.Pixar,
		name: "Pixar",
		prompt: "Transform the image into disney pixar style",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo-to-anime.webp`,
	},
	{
		id: PhotoStyleID.Barbie,
		name: "Barbie",
		prompt: "Transform the image into Barbie doll style",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo-to-anime.webp`,
	},
	{
		id: PhotoStyleID.SimpsonStyle,
		name: "Simpsons",
		prompt: "Transform the image into simpsons style",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo-to-anime.webp`,
	},
	{
		id: PhotoStyleID.Chibi,
		name: "Chibi",
		prompt: "Transform the image into Chibi style",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo-to-anime.webp`,
	},
	{
		id: PhotoStyleID.SouthPark,
		name: "South Park",
		prompt: "Turn this image into a South Park-style flat 2D cutout animation with bold outlines, minimalistic shapes, and a raw, satirical edge.",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo-to-anime.webp`,
	},
	{
		id: PhotoStyleID.Clay,
		name: "Clay",
		prompt: "Transform the image into a claymation",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo-to-anime.webp`,
	},
	{
		id: PhotoStyleID.Muppet,
		name: "Muppet",
		prompt: "Transform the image into Muppets style",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo-to-anime.webp`,
	},
	{
		id: PhotoStyleID.RickMorty,
		name: "Rick Morty",
		prompt: "Transform the image into Rick and Morty style",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo-to-anime.webp`,
	},

	{
		id: PhotoStyleID.Outline,
		name: "Outline",
		prompt: "Transform the image into black and white outline drawing style",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo-to-anime.webp`,
	},
	{
		id: PhotoStyleID.LineArt,
		name: "Line Art",
		prompt: "Transform the image into line art style: A minimalist white and black stylized artwork of the subject , having fewer and thinner lines. The image should depict the subject in a flowing, abstract design using very sleek, continuous line drawing. The background remains white to emphasize the minimalist aesthetic and the elegance of the slim black line art.",
		previewImageDemo: `https://static.youstylize.com/mkt/tools/photo-to-anime.webp`,
	},
];

// export const getStyleType = (categoryId: number, styleId: number): StyleType | null => {
// 	const photoStyle = getPhotoStyleTypes.find((photoStyle) => photoStyle.category.id === categoryId);
// 	if (!photoStyle) {
// 		return null;
// 	}
// 	return photoStyle.style.find((style) => style.id === styleId) ?? null;
// // };
// export const getCategoryAndStyleType = (categoryId: number, styleId: number): { category: StyleCategory | null; style: StyleType | null } => {
// 	const photoStyle = getPhotoStyleTypes.find((photoStyle) => photoStyle.category.id === categoryId);
// 	if (!photoStyle) {
// 		return { category: null, style: null };
// 	}
// 	const style = photoStyle.style.find((style) => style.id === styleId) ?? null;
// 	return { category: photoStyle.category, style };
// };

// export type PhotoCategoryIdAndStyle = {
// 	categoryId: number;
// 	style: StyleType;
// };
// export const getPhotoCategoryIdAndStyle = (categoryId: number, styleId: number): PhotoCategoryIdAndStyle | null => {
// 	if (categoryId === StyleCategoryID.None) {
// 		return {
// 			categoryId: StyleCategoryID.None,
// 			style: { id: PhotoStyleID.None, name: "Custom" },
// 		};
// 	}
// 	if (styleId === StyleCategoryID.Reference) {
// 		return {
// 			categoryId: StyleCategoryID.Reference,
// 			style: { id: PhotoStyleID.Reference, name: "Reference" },
// 		};
// 	}
// 	const style = getStyleType(categoryId, styleId);
// 	if (style) {
// 		return { categoryId: categoryId, style };
// 	}
// 	return null;
// };
