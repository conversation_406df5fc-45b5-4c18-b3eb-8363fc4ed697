import { ParamsError } from "@/@types/error";
import { RectangleVertical, Square, RectangleHorizontal } from "lucide-react";
import { OSS_URL_HOST } from "./constants";

export const getAspectRatioIcon = (aspectRatio: string) => {
	switch (aspectRatio) {
		case "16:9":
			return RectangleHorizontal;
		case "9:16":
			return RectangleVertical;
		case "1:1":
			return Square;
		case "9:21":
			return RectangleVertical;
		default:
			throw new ParamsError("Invalid aspect ratio");
	}
};
export const getAspectRatioClass = (aspectRatio: string) => {
	switch (aspectRatio) {
		case "16:9":
			return "aspect-video";
		case "9:16":
			return "aspect-9/16";
		case "1:1":
			return "aspect-square";
		default:
			throw new ParamsError("Invalid aspect ratio");
	}
};
export const getVideoModelCredits = (model: VideoModel, duration: number, resolution?: string | null) => {
	if (model.creditsMethod === "per_second") {
		return duration * model.credits.perSecond!;
	}
	if (model.creditsMethod === "onetime") {
		return model.credits.onetime!;
	}
	if (model.creditsMethod === "per_second_with_resolution") {
		const resolutionCredits = model.credits.per_second_with_resolution?.find((r) => r.resolution === resolution);
		if (!resolutionCredits) {
			throw new ParamsError("Invalid resolution");
		}
		return duration * resolutionCredits.per_second;
	}
	throw new ParamsError("Invalid credits method");
};

export type VideoModel = {
	name: string;
	id: string; // for api, db
	model: string; // for image generatiton third platform api
	modelStyle?: {
		id: string | null;
		name: string;
	}[];
	aspectRatioAll?: string[];
	durationAll: number[];
	resolutionAll?: string[];
	credits: {
		perSecond?: number;
		onetime?: number;
		per_second_with_resolution?: {
			resolution: string;
			per_second: number;
		}[];
	}; // credits per second
	creditsMethod?: "per_second" | "onetime" | "per_second_with_resolution";
	textToVideo: boolean;
	imageToVideo: boolean;
	imageToVideoNotAspectRatio?: boolean;
	time?: number; // seconds
	logo?: string;
};
export const LTX_13B_DISTILLED: VideoModel = {
	name: "LTX Video",
	id: "ltx-video-13b-distilled",
	model: "ltx-video-13b-distilled",
	credits: { onetime: 5 },
	creditsMethod: "onetime",
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [4],
	textToVideo: true,
	imageToVideo: true,
	time: 40,
	logo: `${OSS_URL_HOST}icon/model/lightricks-dark.webp`,
};
export const KLING_2_1_MASTER: VideoModel = {
	name: "kling 2.1 Master",
	id: "kling-2.1-master",
	model: "kling-2.1-master",
	credits: { perSecond: 31 },
	creditsMethod: "per_second",
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5, 10],
	textToVideo: true,
	imageToVideo: true,
	time: 60 * 5,
	logo: `${OSS_URL_HOST}icon/model/kling-color.webp`,
};
export const KLING_2_1_PRO: VideoModel = {
	name: "kling 2.1 Pro",
	id: "kling-2.1-pro",
	model: "kling-2.1-pro",
	credits: { perSecond: 10 },
	creditsMethod: "per_second",
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5, 10],
	textToVideo: false,
	imageToVideo: true,
	time: 60 * 3,
	logo: `${OSS_URL_HOST}icon/model/kling-color.webp`,
};
export const KLING_2_1_STANDARD: VideoModel = {
	name: "kling 2.1 Standard",
	id: "kling-2.1-standard",
	model: "kling-2.1-standard",
	credits: { perSecond: 6 },
	creditsMethod: "per_second",
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5, 10],
	textToVideo: false,
	imageToVideo: true,
	time: 90,
	logo: `${OSS_URL_HOST}icon/model/kling-color.webp`,
};
export const KLING_2_MASTER: VideoModel = {
	name: "kling 2 Master",
	id: "kling-2-master",
	model: "kling-2-master",
	credits: { perSecond: 31 },
	creditsMethod: "per_second",
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5, 10],
	textToVideo: true,
	imageToVideo: false,
	time: 60 * 5,
	logo: `${OSS_URL_HOST}icon/model/kling-color.webp`,
};
export const KLING_1_6_PRO: VideoModel = {
	name: "kling 1.6 Pro",
	id: "kling-1.6-pro",
	model: "kling-1.6-pro",
	credits: { perSecond: 10 },
	creditsMethod: "per_second",
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5, 10],
	textToVideo: true,
	imageToVideo: false,
	time: 60 * 4,
	logo: `${OSS_URL_HOST}icon/model/kling-color.webp`,
};
export const KLING_1_6_STANDARD: VideoModel = {
	name: "kling 1.6 Standard",
	id: "kling-1.6-standard",
	model: "kling-1.6-standard",
	credits: { perSecond: 5 },
	creditsMethod: "per_second",
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5, 10],
	textToVideo: true,
	imageToVideo: false,
	time: 60 * 4,
	logo: `${OSS_URL_HOST}icon/model/kling-color.webp`,
};
export const GOOGLE_VEO_3: VideoModel = {
	name: "Google Veo 3",
	id: "google-veo-3",
	model: "google-veo-3",
	credits: { perSecond: 83 },
	creditsMethod: "per_second",
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [8],
	textToVideo: true,
	imageToVideo: false,
	time: 60 * 2,
	logo: `${OSS_URL_HOST}icon/model/deepmind-color.webp`,
};
export const SEEDANCE_1_LITE: VideoModel = {
	name: "Seedance 1 Lite",
	id: "seedance-1-lite",
	model: "seedance/v1/lite",
	credits: { perSecond: 4 },
	creditsMethod: "per_second",
	aspectRatioAll: ["16:9", "1:1", "9:21"],
	durationAll: [5, 10],
	resolutionAll: ["720p"],
	textToVideo: true,
	imageToVideo: true,
	imageToVideoNotAspectRatio: true,
	time: 60,
	logo: `${OSS_URL_HOST}icon/model/bytedance-color.webp`,
};
export const SEEDANCE_1_PRO: VideoModel = {
	name: "Seedance 1 Pro",
	id: "seedance-1-pro",
	model: "seedance/v1/pro",
	credits: { perSecond: 17 },
	creditsMethod: "per_second",
	aspectRatioAll: ["16:9", "1:1", "9:16", "9:21"],
	durationAll: [5, 10],
	resolutionAll: ["1080p"],
	textToVideo: true,
	imageToVideo: true,
	imageToVideoNotAspectRatio: true,
	time: 60 * 2,
	logo: `${OSS_URL_HOST}icon/model/bytedance-color.webp`,
};
export const PIXVERSE_4_5_FAST: VideoModel = {
	name: "Pixverse 4.5 Fast",
	id: "pixverse-4.5-fast",
	model: "pixverse/v4.5/fast",
	modelStyle: [
		{
			id: null,
			name: "Auto",
		},
		{
			id: "anime",
			name: "Anime",
		},
		{
			id: "3d_animation",
			name: "3D Animation",
		},
		{
			id: "clay",
			name: "Clay",
		},
		{
			id: "comic",
			name: "Comic",
		},
		{
			id: "cyberpunk",
			name: "Cyberpunk",
		},
	],
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5],
	resolutionAll: ["360p", "480p", "720p"],
	credits: {
		per_second_with_resolution: [
			{
				resolution: "360p",
				per_second: 14,
			},
			{
				resolution: "480p",
				per_second: 14,
			},
			{
				resolution: "720p",
				per_second: 18,
			},
		],
	},
	creditsMethod: "per_second_with_resolution",
	textToVideo: true,
	imageToVideo: true,
	time: 100,
	logo: `${OSS_URL_HOST}icon/model/pixverse-color.webp`,
};
export const PIXVERSE_4_5: VideoModel = {
	name: "Pixverse 4.5",
	id: "pixverse-4.5",
	model: "pixverse/v4.5",
	modelStyle: [
		{
			id: null,
			name: "Auto",
		},
		{
			id: "anime",
			name: "Anime",
		},
		{
			id: "3d_animation",
			name: "3D Animation",
		},
		{
			id: "clay",
			name: "Clay",
		},
		{
			id: "comic",
			name: "Comic",
		},
		{
			id: "cyberpunk",
			name: "Cyberpunk",
		},
	],
	aspectRatioAll: ["16:9", "9:16", "1:1"],
	durationAll: [5, 8],
	resolutionAll: ["360p", "480p", "720p", "1080p"],
	credits: {
		per_second_with_resolution: [
			{
				resolution: "360p",
				per_second: 7,
			},
			{
				resolution: "480p",
				per_second: 7,
			},
			{
				resolution: "720p",
				per_second: 9,
			},
			{
				resolution: "1080p",
				per_second: 18,
			},
		],
	},
	creditsMethod: "per_second_with_resolution",
	textToVideo: true,
	imageToVideo: true,
	time: 100,
	logo: `${OSS_URL_HOST}icon/model/pixverse-color.webp`,
};
export const HAILUO_2_STANDARD: VideoModel = {
	name: "Hailuo 2 Standard",
	id: "hailuo-02-standard",
	model: "hailuo-02/standard",
	credits: { perSecond: 5 },
	creditsMethod: "per_second",
	durationAll: [6, 10],
	resolutionAll: ["768p"],
	textToVideo: true,
	imageToVideo: true,
	time: 60 * 6,
	logo: `${OSS_URL_HOST}icon/model/hailuo-color.webp`,
};
export const HAILUO_2_PRO: VideoModel = {
	name: "Hailuo 2 Pro",
	id: "hailuo-02-pro",
	model: "hailuo-02/pro",
	credits: { perSecond: 9 },
	creditsMethod: "per_second",
	durationAll: [5],
	resolutionAll: ["1080p"],
	textToVideo: true,
	imageToVideo: true,
	time: 60 * 8,
	logo: `${OSS_URL_HOST}icon/model/hailuo-color.webp`,
};

const videoModels: VideoModel[] = [
	LTX_13B_DISTILLED,
	GOOGLE_VEO_3,
	KLING_2_1_MASTER,
	KLING_2_1_PRO,
	KLING_2_1_STANDARD,
	HAILUO_2_PRO,
	HAILUO_2_STANDARD,
	PIXVERSE_4_5,
	PIXVERSE_4_5_FAST,
	SEEDANCE_1_PRO,
	SEEDANCE_1_LITE,
	KLING_2_MASTER,
	KLING_1_6_STANDARD,
];
export const textToVideoModels: VideoModel[] = videoModels.filter((videoModel) => videoModel.textToVideo);
export const imageToVideoModels: VideoModel[] = videoModels.filter((videoModel) => videoModel.imageToVideo);

export const pageVideoModels: VideoModel[] = [
	GOOGLE_VEO_3,
	KLING_2_1_MASTER,
	KLING_2_1_PRO,
	KLING_2_1_STANDARD,
	HAILUO_2_PRO,
	HAILUO_2_STANDARD,
	PIXVERSE_4_5,
	PIXVERSE_4_5_FAST,
	SEEDANCE_1_PRO,
	SEEDANCE_1_LITE,
];

export const getVideoModel = (id: string): VideoModel => {
	const videoModel = videoModels.find((videoModel) => videoModel.id === id);
	if (process.env.NODE_ENV === "development") {
		console.log("videoModel: ", videoModel);
	}
	if (!videoModel) {
		throw new ParamsError("Video model is not found.");
	}
	return videoModel;
};
