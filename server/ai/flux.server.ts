import { FLUX_1_1_PRO, FLUX_1_KONTEXT_MAX, FLUX_1_KONTEXT_PRO } from "@/lib/utils-image-model";
import { ofetch } from "ofetch";
import { fal } from "./fal-config.server";
import { QueueStatus } from "@fal-ai/client";

export async function genFluxProFromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
	image?: string,
): Promise<string[]> {
	let falAIEndPoint = "fal-ai/flux-pro/v1.1";
	let payload: any = {
		prompt: prompt,
		num_images: numImages,
	};
	if (model === FLUX_1_1_PRO.model) {
		payload.image_size = {
			width: aspectRatio.width,
			height: aspectRatio.height,
		};
	} else {
		payload.aspect_ratio = aspectRatio.ratio;
		falAIEndPoint = "fal-ai/flux-pro/v1.1-ultra";
	}
	if (image) {
		payload.image_url = image;
		falAIEndPoint += "/redux";
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai flux1.1 pro payload: ", payload);
		console.log("fal.ai flux1.1 pro falRequestModelName:", falAIEndPoint);
	}
	const { request_id: requestId } = await fal.queue.submit(falAIEndPoint, {
		input: payload,
	});

	let resultUrls: string[] | null = null;
	if (model === FLUX_1_1_PRO.model) {
		await new Promise((resolve) => setTimeout(resolve, 5000)); // wait for 5 seconds
	} else {
		await new Promise((resolve) => setTimeout(resolve, 10000)); // wait for 10 seconds
	}
	while (true) {
		const { status }: QueueStatus = await fal.queue.status(falAIEndPoint, {
			requestId: requestId,
		});
		// status: IN_QUEUE, IN_PROGRESS, COMPLETED
		if (status === "COMPLETED") {
			const result = await fal.queue.result(falAIEndPoint, {
				requestId: requestId,
			});
			resultUrls = result.data.images.map((image: any) => image.url);
			break;
		} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
		} else {
			throw new Error(`Failed to generate image.`);
		}

		await new Promise((resolve) => setTimeout(resolve, 2000)); // wait for 2 seconds
	}

	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}

export async function genFluxProKontextFromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio?: {
		ratio: string;
		width: number;
		height: number;
	},
	image?: string,
): Promise<string[]> {
	let falAIEndPoint = "fal-ai/flux-pro/kontext";
	let payload: any = {
		prompt: prompt,
		num_images: numImages,
	};
	if (model === FLUX_1_KONTEXT_MAX.model) {
		falAIEndPoint = "fal-ai/flux-pro/kontext/max";
	}
	if (image) {
		payload.image_url = image;
	} else {
		if (aspectRatio) {
			payload.aspect_ratio = aspectRatio.ratio;
		}
		falAIEndPoint += "/text-to-image";
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai flux1 kontext payload: ", payload);
		console.log("fal.ai flux1 kontext endpoint:", falAIEndPoint);
	}

	const { request_id: requestId } = await fal.queue.submit(falAIEndPoint, {
		input: payload,
	});

	let resultUrls: string[] | null = null;
	if (model === FLUX_1_KONTEXT_PRO.model) {
		await new Promise((resolve) => setTimeout(resolve, 4500)); // wait for 4.5 seconds
	} else {
		await new Promise((resolve) => setTimeout(resolve, 8000)); // wait for 8 seconds
	}
	while (true) {
		const { status }: QueueStatus = await fal.queue.status(falAIEndPoint, {
			requestId: requestId,
		});
		// status: IN_QUEUE, IN_PROGRESS, COMPLETED
		if (status === "COMPLETED") {
			const result = await fal.queue.result(falAIEndPoint, {
				requestId: requestId,
			});
			resultUrls = result.data.images.map((image: any) => image.url);
			break;
		} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
		} else {
			throw new Error(`Failed to generate image.`);
		}

		await new Promise((resolve) => setTimeout(resolve, 2000)); // wait for 2 seconds
	}

	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}

export async function genFluxKontextDevFromFal(prompt: string, numImages: number, image: string): Promise<string[]> {
	let payload: any = {
		prompt: prompt,
		num_images: numImages,
		image_url: image,
		enable_safety_checker: false,
		resolution_mode: "auto",
	};

	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai flux1 kontext dev payload: ", payload);
	}

	const { request_id: requestId } = await fal.queue.submit("fal-ai/flux-kontext/dev", {
		input: payload,
	});

	await new Promise((resolve) => setTimeout(resolve, 4000)); // wait for 4 seconds

	let resultUrls: string[] | null = null;
	while (true) {
		const { status } = await fal.queue.status("fal-ai/flux-kontext/dev", {
			requestId: requestId,
		});
		// status: IN_QUEUE, IN_PROGRESS, COMPLETED
		if (status === "COMPLETED") {
			const result = await fal.queue.result("fal-ai/flux-kontext/dev", {
				requestId: requestId,
			});
			resultUrls = result.data.images.map((image: any) => image.url);
			break;
		} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
		} else {
			throw new Error(`Failed to generate image.`);
		}

		await new Promise((resolve) => setTimeout(resolve, 1500)); // wait for 1.5 seconds
	}

	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}

export async function genFluxFromWavespeed(
	model: string,
	prompt: string,
	numImages: number,
	size: {
		width: number;
		height: number;
	},
	image?: string,
): Promise<string[]> {
	let payload: any = {
		num_images: numImages,
		prompt: prompt,
		size: `${size.width}*${size.height}`,
	};
	if (image) {
		payload.image = image;
	}

	if (process.env.NODE_ENV === "development") {
		console.log("wavespeed payload: ", payload);
	}
	// throw new Error("wavespeed test error");

	const { code, message, data } = await ofetch(`https://api.wavespeed.ai/api/v2/wavespeed-ai/${model}`, {
		method: "POST",
		headers: {
			"Content-Type": "application/json",
			Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
		},
		body: payload,
	});
	if (code !== 200) {
		throw new Error(`Failed to generate image. ${message}`);
	}
	// console.log("data: ", data);
	const requestId = data.id;
	const getUrl = data.urls.get;

	let resultUrls: string[] | null = null;
	await new Promise((resolve) => setTimeout(resolve, 4000)); // wait for 4 seconds
	while (true) {
		const {
			code: resultCode,
			message: resultMessage,
			data: resutlData,
		} = await ofetch(`https://api.wavespeed.ai/api/v2/predictions/${requestId}/result`, {
			headers: {
				Authorization: `Bearer ${process.env.WAVESPEED_API_KEY}`,
			},
		});
		if (resultCode !== 200) {
			console.error(`Error: ${resultCode}, ${resultMessage}`);
			throw new Error(`Failed to get image result. ${resultMessage}`);
		}
		const { status, outputs, error } = resutlData;
		// status: processing, completed, failed
		if (status === "completed") {
			resultUrls = outputs;
			// console.log("Task completed. URL:", resultUrls);
			break;
		} else if (status === "failed") {
			console.error("Task failed:", error);
			throw new Error(`Failed to generate image. Result: ${error}`);
		} else {
			// console.log("Task still processing. Status:", status);
		}

		await new Promise((resolve) => setTimeout(resolve, 1500)); // wait for 1.5 seconds
	}
	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}
