import { fal } from "./fal-config.server";
import { QueueStatus } from "@fal-ai/client";

export async function genIdeogramFromFal(
	model: string,
	prompt: string,
	numImages: number,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
	image?: string,
): Promise<string[]> {
	let falAIEndPoint = "fal-ai/ideogram/v3";
	let payload: any = {
		prompt: prompt,
		rendering_speed: "TURBO",
		num_images: numImages,
		image_size: {
			width: aspectRatio.width,
			height: aspectRatio.height,
		},
	};
	if (image) {
		payload.image_urls = [image];
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai ideogram 3 payload: ", payload);
		console.log("fal.ai ideogram 3 fal.ai endpoint: ", falAIEndPoint);
	}

	const { request_id: requestId } = await fal.queue.submit(falAIEndPoint, {
		input: payload,
	});

	let resultUrls: string[] | null = null;
	await new Promise((resolve) => setTimeout(resolve, 10000)); // wait for 10 seconds
	while (true) {
		const { status }: QueueStatus = await fal.queue.status(falAIEndPoint, {
			requestId: requestId,
			logs: true,
		});

		// status: IN_QUEUE, IN_PROGRESS, COMPLETED
		if (status === "COMPLETED") {
			const result = await fal.queue.result(falAIEndPoint, {
				requestId: requestId,
			});
			resultUrls = result.data.images.map((image: any) => image.url);
			break;
		} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
		} else {
			throw new Error(`Failed to generate image.`);
		}

		await new Promise((resolve) => setTimeout(resolve, 2000)); // wait for 2 seconds
	}

	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}
