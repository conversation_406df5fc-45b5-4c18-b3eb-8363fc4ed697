import { fal } from "./fal-config.server";
import { QueueStatus } from "@fal-ai/client";

export async function genGoogleImagen4FromFal(model: string, prompt: string, numImages: number, aspectRatio: string): Promise<string[]> {
	let falAIEndPoint = `fal-ai/${model}`;
	let payload: any = {
		prompt: prompt,
		aspect_ratio: aspectRatio,
		num_images: numImages,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai imagen 4 payload: ", payload);
		console.log("fal.ai imagen 4 fal.ai endpoint: ", falAIEndPoint);
	}

	const { request_id: requestId } = await fal.queue.submit(falAIEndPoint, {
		input: payload,
	});

	let resultUrls: string[] | null = null;
	await new Promise((resolve) => setTimeout(resolve, 6000)); // wait for 6 seconds
	while (true) {
		const { status }: QueueStatus = await fal.queue.status(falAIEndPoint, {
			requestId: requestId,
		});

		// status: IN_QUEUE, IN_PROGRESS, COMPLETED
		if (status === "COMPLETED") {
			const result = await fal.queue.result(falAIEndPoint, {
				requestId: requestId,
			});
			resultUrls = result.data.images.map((image: any) => image.url);
			break;
		} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
		} else {
			throw new Error(`Failed to generate image.`);
		}

		await new Promise((resolve) => setTimeout(resolve, 2000)); // wait for 2 seconds
	}

	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}

export async function genGoogleImagen3FromFal(model: string, prompt: string, numImages: number, aspectRatio: string): Promise<string[]> {
	let falAIEndPoint = `fal-ai/${model}`;
	let payload: any = {
		prompt: prompt,
		aspect_ratio: aspectRatio,
		num_images: numImages,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai imagen 3 payload: ", payload);
		console.log("fal.ai imagen 3 fal.ai endpoint: ", falAIEndPoint);
	}

	const { request_id: requestId } = await fal.queue.submit(falAIEndPoint, {
		input: payload,
	});

	let resultUrls: string[] | null = null;
	await new Promise((resolve) => setTimeout(resolve, 6000)); // wait for 6 seconds
	while (true) {
		const { status }: QueueStatus = await fal.queue.status(falAIEndPoint, {
			requestId: requestId,
		});
		// status: IN_QUEUE, IN_PROGRESS, COMPLETED
		if (status === "COMPLETED") {
			const result = await fal.queue.result(falAIEndPoint, {
				requestId: requestId,
			});
			resultUrls = result.data.images.map((image: any) => image.url);
			break;
		} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
		} else {
			throw new Error(`Failed to generate image.`);
		}

		await new Promise((resolve) => setTimeout(resolve, 2000)); // wait for 2 seconds
	}

	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}
