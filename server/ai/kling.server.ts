import { fal } from "@fal-ai/client";
import { CALLBACK_URL_FAL } from "@/lib/constants";

export async function genKling1Point6FromFal(model: string, prompt: string, duration: number, aspectRatio: string): Promise<string> {
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		aspect_ratio: aspectRatio,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai kling 1.6 payload: ", payload);
	}

	fal.config({
		credentials: process.env.FAL_API_KEY,
	});
	const { request_id } = await fal.queue.submit("fal-ai/kling-video/v1.6/standard/text-to-video", {
		input: payload,
		webhookUrl: CALLBACK_URL_FAL,
	});

	return request_id;
}

export async function genKling2Point1MasterFromFal(
	model: string,
	prompt: string,
	duration: number,
	aspectRatio: string,
	image?: string | null,
): Promise<string> {
	// let falRequestModelName = "v1.1";
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		aspect_ratio: aspectRatio,
	};
	if (image) {
		payload.image_url = image;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Kling 2.1 Master payload: ", payload);
	}

	fal.config({
		credentials: process.env.FAL_API_KEY,
	});
	const { request_id } = await fal.queue.submit(`fal-ai/kling-video/v2.1/master/${image ? "image-to-video" : "text-to-video"}`, {
		input: payload,
		webhookUrl: CALLBACK_URL_FAL,
	});

	return request_id;
}

export async function genKling2Point1ProFromFal(model: string, prompt: string, duration: number, aspectRatio: string, image: string | null): Promise<string> {
	// let falRequestModelName = "v1.1";
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		aspect_ratio: aspectRatio,
		image_url: image,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Kling 2.1 Pro payload: ", payload);
	}

	fal.config({
		credentials: process.env.FAL_API_KEY,
	});
	const { request_id } = await fal.queue.submit(`fal-ai/kling-video/v2.1/pro/image-to-video`, {
		input: payload,
		webhookUrl: CALLBACK_URL_FAL,
	});

	return request_id;
}
export async function genKling2Point1StandardFromFal(
	model: string,
	prompt: string,
	duration: number,
	aspectRatio: string,
	image: string | null,
): Promise<string> {
	// let falRequestModelName = "v1.1";
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		aspect_ratio: aspectRatio,
		image_url: image,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Kling 2.1 Standard payload: ", payload);
	}

	fal.config({
		credentials: process.env.FAL_API_KEY,
	});
	const { request_id } = await fal.queue.submit(`fal-ai/kling-video/v2.1/standard/image-to-video`, {
		input: payload,
		webhookUrl: CALLBACK_URL_FAL,
	});

	return request_id;
}

export async function genKling2FromFal(model: string, prompt: string, duration: number, aspectRatio: string): Promise<string> {
	// let falRequestModelName = "v1.1";
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		aspect_ratio: aspectRatio,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Kling 2 payload: ", payload);
	}

	fal.config({
		credentials: process.env.FAL_API_KEY,
	});
	const { request_id } = await fal.queue.submit("fal-ai/kling-video/v2/master/text-to-video", {
		input: payload,
		webhookUrl: CALLBACK_URL_FAL,
	});

	return request_id;
}
