import { fal } from "@fal-ai/client";
import { CALLBACK_URL_FAL } from "@/lib/constants";

export async function genLTXFromFal(model: string, prompt: string, aspectRatio: string, image?: string | null): Promise<string> {
	// let falRequestModelName = "v1.1";
	let payload: any = {
		prompt: prompt,
		aspect_ratio: aspectRatio,
	};
	if (image) {
		payload.image_url = image;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai LTX payload: ", payload);
	}

	fal.config({
		credentials: process.env.FAL_API_KEY,
	});
	const { request_id } = await fal.queue.submit(`fal-ai/ltx-video-13b-distilled${image ? "/image-to-video" : ""}`, {
		input: payload,
		webhookUrl: CALLBACK_URL_FAL,
	});

	return request_id;
}
