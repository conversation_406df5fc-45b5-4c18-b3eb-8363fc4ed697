import { fal } from "./fal-config.server";
import { QueueStatus } from "@fal-ai/client";

export async function genRecraft3FromFal(
	model: string,
	prompt: string,
	aspectRatio: {
		ratio: string;
		width: number;
		height: number;
	},
	image?: string,
	modelStyle?: string | null,
): Promise<string[]> {
	let falAIEndPoint = "fal-ai/recraft/v3/text-to-image";
	let payload: any = {
		prompt: prompt,
	};
	if (image) {
		payload.image_url = image;
		payload.strength = 0.5;
		falAIEndPoint = "fal-ai/recraft/v3/image-to-image";
	} else {
		payload.image_size = {
			width: aspectRatio.width,
			height: aspectRatio.height,
		};
	}
	if (modelStyle) {
		payload.style = modelStyle;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai recraft 3 payload: ", payload);
		console.log("fal.ai recraft 3 fal.ai endpoint: ", falAIEndPoint);
	}

	const { request_id: requestId } = await fal.queue.submit(falAIEndPoint, {
		input: payload,
	});

	let resultUrls: string[] | null = null;
	await new Promise((resolve) => setTimeout(resolve, 9000)); // wait for 9 seconds
	while (true) {
		const { status }: QueueStatus = await fal.queue.status(falAIEndPoint, {
			requestId: requestId,
		});
		// status: IN_QUEUE, IN_PROGRESS, COMPLETED
		if (status === "COMPLETED") {
			const result = await fal.queue.result(falAIEndPoint, {
				requestId: requestId,
			});
			resultUrls = result.data.images.map((image: any) => image.url);
			break;
		} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
		} else {
			throw new Error(`Failed to generate image.`);
		}

		await new Promise((resolve) => setTimeout(resolve, 1500)); // wait for 1.5 seconds
	}

	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}
