import { CALLBACK_URL_FAL } from "@/lib/constants";
import { fal } from "./fal-config.server";

export async function genSeedance1LiteFromFal(model: string, prompt: string, duration: number, aspectRatio: string, image?: string | null): Promise<string> {
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		resolution: "720p",
	};
	if (image) {
		payload.image_url = image;
	} else {
		payload.aspect_ratio = aspectRatio;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Seedance 1 lite payload: ", payload);
	}

	const { request_id } = await fal.queue.submit(`fal-ai/bytedance/seedance/v1/lite/${image ? "image-to-video" : "text-to-video"}`, {
		input: payload,
		webhookUrl: CALLBACK_URL_FAL,
	});

	return request_id;
}

export async function genSeedance1ProFromFal(model: string, prompt: string, duration: number, aspectRatio: string, image?: string | null): Promise<string> {
	let payload: any = {
		prompt: prompt,
		duration: duration.toString(),
		resolution: "1080p",
	};
	if (image) {
		payload.image_url = image;
	} else {
		payload.aspect_ratio = aspectRatio;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Seedance 1 pro payload: ", payload);
	}

	const { request_id } = await fal.queue.submit(`fal-ai/bytedance/seedance/v1/pro/${image ? "image-to-video" : "text-to-video"}`, {
		input: payload,
		webhookUrl: CALLBACK_URL_FAL,
	});

	return request_id;
}
