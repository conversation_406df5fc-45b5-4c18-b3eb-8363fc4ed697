import { fal } from "./fal-config.server";
import { QueueStatus } from "@fal-ai/client";

export async function genSeedream3FromFal(model: string, prompt: string, numImages: number, aspectRatio: string): Promise<string[]> {
	const payload: any = {
		prompt: prompt,
		num_images: numImages,
		aspect_ratio: aspectRatio,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Seedream 3 payload: ", payload);
	}

	const { request_id: requestId } = await fal.queue.submit("fal-ai/bytedance/seedream/v3/text-to-image", {
		input: payload,
	});

	let resultUrls: string[] | null = null;
	await new Promise((resolve) => setTimeout(resolve, 5000)); // wait for 8 seconds
	while (true) {
		const status: QueueStatus = await fal.queue.status("fal-ai/bytedance/seedream/v3/text-to-image", {
			requestId: requestId,
		});

		// status: IN_QUEUE, IN_PROGRESS, COMPLETED
		if (status.status === "COMPLETED") {
			const result = await fal.queue.result("fal-ai/bytedance/seedream/v3/text-to-image", {
				requestId: requestId,
			});
			resultUrls = result.data.images.map((image: any) => image.url);
			break;
		} else if (status.status === "IN_PROGRESS" || status.status === "IN_QUEUE") {
		} else {
			throw new Error(`Failed to generate image.`);
		}

		await new Promise((resolve) => setTimeout(resolve, 2000)); // wait for 2 seconds
	}

	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}
