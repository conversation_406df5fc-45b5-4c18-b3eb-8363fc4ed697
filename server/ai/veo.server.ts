import { fal } from "@fal-ai/client";
import { CALLBACK_URL_FAL } from "@/lib/constants";

export async function genVeoFromFal(model: string, prompt: string, duration: number, aspectRatio: string): Promise<string> {
	// let falRequestModelName = "v1.1";
	let payload: any = {
		prompt: prompt,
		duration: `${duration.toString()}s`,
		aspect_ratio: aspectRatio,
	};
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai Veo 3 payload: ", payload);
	}

	fal.config({
		credentials: process.env.FAL_API_KEY,
	});
	const { request_id } = await fal.queue.submit("fal-ai/veo3", {
		input: payload,
		webhookUrl: CALLBACK_URL_FAL,
	});

	return request_id;
}
