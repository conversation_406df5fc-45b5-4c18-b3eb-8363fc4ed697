import { betterAuth, BetterAuthOptions } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { MembershipID, membershipMapping } from "@/@types/membership-type";
import * as schema from "../db/schema.server";
import { endOfMonth } from "date-fns";
import { COOKIE_BROWSER_USER_ID, WEBHOST } from "@/lib/constants";
import { getDB } from "../db/db-client.server";
import { checkCfIpCountry, getCfIpAndCountry } from "./auth-ipcountry";
import { userSchema } from "../db/schema.server";
import { eq } from "drizzle-orm";
import { cookies } from "next/headers";

export const getAuth = () => {
	return betterAuth({
		baseURL: WEBHOST,
		secret: process.env.AUTH_SECRET || undefined,
		database: drizzleAdapter(getDB(), {
			provider: "sqlite",
			schema: {
				user: schema.userSchema,
				account: schema.accountSchema,
				session: schema.sessionSchema,
				verification: schema.verificationSchema,
			},
		}),
		socialProviders: {
			google: {
				clientId: process.env.AUTH_GOOGLE_ID!,
				clientSecret: process.env.AUTH_GOOGLE_SECRET!,
			},
		},
		// advanced: {
		// 	generateId: false,
		// },
		// user: {
		// 	additionalFields: {
		// 		membershipId: {
		// 			type: "number",
		// 			required: false,
		// 			defaultValue: 0,
		// 			input: false, // don't allow user to set role
		// 		},
		// 		membershipFormatted: {
		// 			type: "string",
		// 			required: false,
		// 			input: false, // don't allow user to set role
		// 		},
		// 		creditFree: {
		// 			type: "number",
		// 			required: false,
		// 			defaultValue: 0,
		// 			input: false, // don't allow user to set role
		// 		},
		// 		creditFreeEndsAt: {
		// 			type: "date",
		// 			required: false,
		// 			input: false, // don't allow user to set role
		// 		},
		// 		countryCode: {
		// 			type: "string",
		// 			required: false,
		// 			input: false, // don't allow user to set role
		// 		},
		// 	},
		// },
		session: {
			expiresIn: 60 * 60 * 24 * 7, // 7 days
			updateAge: 60 * 60 * 24, // 1 day (every 1 day the session expiration is updated)
			cookieCache: {
				enabled: true,
				maxAge: 60 * 60 * 24, // 1 day
			},
		},
		databaseHooks: {
			user: {
				create: {
					// before: async (user) => {
					// 	const { countryCode, ip } = await getCfIpAndCountry();
					// 	const isBanIpCountry = await checkCfIpCountry(countryCode);
					// 	const freeMembership = membershipMapping[MembershipID.Free];
					// 	let freeCredits = isBanIpCountry ? 0 : freeMembership.credits;

					// 	return {
					// 		data: {
					// 			...user,
					// 			membershipId: freeMembership.id,
					// 			membershipFormatted: freeMembership.name,
					// 			creditFree: freeCredits,
					// 			creditFreeEndsAt: endOfMonth(new Date()),
					// 			subscriptionPeriod: MembershipPeriodNone.value,
					// 			countryCode: countryCode,
					// 			ip: ip,
					// 		},
					// 	};
					// },
					after: async (user) => {
						const userId = user.id;
						let remark: string | null = null;

						const { countryCode, ip } = await getCfIpAndCountry();
						const isBanIpCountry = await checkCfIpCountry(countryCode);

						const freeMembership = membershipMapping[MembershipID.Free];
						let freeCredits = isBanIpCountry ? 0 : freeMembership.credits;

						// if (ip) {
						// 	const db = getDB();
						// 	const [userWithIp]: User[] = await db.select().from(userSchema).where(eq(userSchema.ip, ip));
						// 	if (userWithIp) {
						// 		freeCredits = 0;
						// 		remark = `New user with an already existing IP: ${ip}. Existing user email: ${userWithIp.email}`;
						// 	}
						// }

						// check browser cookie
						const cookieStore = await cookies();
						const lastUserId = cookieStore.get(COOKIE_BROWSER_USER_ID)?.value;
						if (lastUserId) {
							freeCredits = 0;
							remark = `New user with an existing browser cookie. Existing user id: ${lastUserId}.`;
						} else {
							cookieStore.set(COOKIE_BROWSER_USER_ID, userId, { path: "/" });
						}

						const db = getDB();
						await db
							.update(userSchema)
							.set({
								// membershipId: freeMembership.id,
								// membershipFormatted: freeMembership.name,
								creditFree: freeCredits,
								creditFreeEndsAt: endOfMonth(new Date()),
								// subscriptionPeriod: MembershipPeriodNone.value,
								countryCode: countryCode,
								ip: ip,
								remark: remark,
							})
							.where(eq(userSchema.id, userId));
					},
				},
			},
		},
	} as BetterAuthOptions);
};
