import { MediaHeadType } from "@/@types/media/media-type";
import { CLOUDFLARE_R2_BUCKET_NAME } from "@/lib/constants";
import { getContentTypeFromImageUrl, getContentTypeFromVideoUrl, getFileExtension } from "@/lib/file/utils-file";
import { getUUIDString } from "@/lib/utils";
import { getCurrentMonthAndDayString } from "@/lib/utils-date";
import { AwsClient } from "aws4fetch";
import { ofetch } from "ofetch";

export async function createR2Url(path: string, contentType: string | null): Promise<string> {
	const R2_URL = `https://${process.env.CLOUDFLARE_ACCOUNT_ID!}.r2.cloudflarestorage.com`;
	const client = new AwsClient({
		service: "s3",
		region: "auto",
		accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
		secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
	});
	const presignedURL = (
		await client.sign(
			new Request(`${R2_URL}/${CLOUDFLARE_R2_BUCKET_NAME}/${path}?X-Amz-Expires=${3600}`, {
				method: "PUT",
			}),
			{
				aws: { signQuery: true },
			},
		)
	).url.toString();

	return presignedURL;
}

// generate file url path for r2=============================================
export function generateFileUrlPath(ext: string): { path: string; fileName: string } {
	const fileId = getUUIDString();
	const { yearMonth, yearMonthDay } = getCurrentMonthAndDayString();
	return { path: `media/${yearMonth}/${yearMonthDay}${fileId}.${ext}`, fileName: `${yearMonthDay}${fileId}.${ext}}` };
}

// save file to r2=============================================
export async function saveFileToR2(path: string, fileBlob: Blob, contentType: string | null, fileName: string): Promise<string | null> {
	if (!path) {
		return null;
	}

	const file = new File([fileBlob], fileName);

	const signedUrl = await createR2Url(path, contentType);
	await ofetch(signedUrl, {
		method: "PUT",
		body: file,
	});

	return path;
}

// save to from r2=============================================
export async function saveToR2(url: string, mediaType: string = MediaHeadType.Image): Promise<string> {
	const contentType = mediaType === MediaHeadType.Image ? getContentTypeFromImageUrl(url) : getContentTypeFromVideoUrl(url);
	let { path: imagePath, fileName } = generateFileUrlPath(getFileExtension(url));
	const fileBlob = await ofetch(url, { responseType: "blob" });

	if (process.env.NODE_ENV !== "production") {
		imagePath = `dev/${imagePath}`;
	}
	const maxRetries = 3;
	let attempt = 0;
	let lastError: Error | null = null;
	while (attempt < maxRetries) {
		try {
			await saveFileToR2(imagePath, fileBlob, contentType, fileName);
			return imagePath;
		} catch (error: any) {
			console.log(`Attempt ${attempt + 1} failed:`, error.message);
			lastError = error as Error;
			attempt++;
			await new Promise((resolve) => setTimeout(resolve, 1000));
		}
	}
	throw lastError!;
}
